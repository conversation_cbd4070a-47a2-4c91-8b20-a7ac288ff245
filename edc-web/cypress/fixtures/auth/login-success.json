{"success": true, "user": {"id": 1, "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": "user", "isActive": true, "preferences": {"theme": "default", "language": "en", "notifications": {"email": true, "push": false, "sms": false}}, "profile": {"avatar": "https://via.placeholder.com/150", "bio": "Test user for Cypress testing", "department": "Engineering", "title": "Software Developer"}, "permissions": ["read_content", "create_content", "edit_own_content"]}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.mock-jwt-token", "refreshToken": "mock-refresh-token", "expiresIn": 3600, "organization": {"id": 1, "name": "Test Organization", "domain": "test.example.com", "settings": {"allowSelfRegistration": true, "requireEmailVerification": false, "passwordPolicy": {"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": false}}}}