{"id": 1, "title": "Introduction to Re<PERSON> Hook<PERSON>", "description": "Learn how to use React Hooks to manage state and side effects in functional components", "content": "React Hooks are a powerful feature that allows you to use state and other React features without writing a class component...", "type": "course", "status": "published", "duration": 120, "difficulty": "intermediate", "rating": 4.6, "enrollmentCount": 1847, "completionRate": 82, "thumbnail": "https://via.placeholder.com/600x400", "videoUrl": "https://example.com/videos/react-hooks-intro.mp4", "author": {"id": 201, "name": "<PERSON>", "avatar": "https://via.placeholder.com/100", "title": "Senior React <PERSON>", "bio": "<PERSON> is a senior developer with 8 years of experience in React and frontend development.", "socialLinks": {"twitter": "https://twitter.com/sarahdev", "linkedin": "https://linkedin.com/in/sarah<PERSON>lson"}}, "category": {"id": 1, "name": "Frontend Development", "slug": "frontend-development"}, "tags": ["react", "hooks", "javascript", "frontend", "state-management"], "skills": [{"id": 1, "name": "React", "level": "intermediate"}, {"id": 2, "name": "JavaScript", "level": "intermediate"}, {"id": 3, "name": "State Management", "level": "beginner"}], "prerequisites": [{"id": 10, "title": "JavaScript Fundamentals", "completed": true}, {"id": 11, "title": "Introduction to React", "completed": true}], "chapters": [{"id": 1, "title": "Introduction to <PERSON><PERSON>", "duration": 15, "completed": true}, {"id": 2, "title": "useState Hook", "duration": 25, "completed": true}, {"id": 3, "title": "useEffect Hook", "duration": 30, "completed": false}, {"id": 4, "title": "Custom Hooks", "duration": 35, "completed": false}, {"id": 5, "title": "Advanced Patterns", "duration": 15, "completed": false}], "comments": [{"id": 1, "user": {"id": 501, "name": "<PERSON>", "avatar": "https://via.placeholder.com/40"}, "content": "Great course! Really helped me understand hooks better.", "rating": 5, "createdAt": "2024-01-10T14:30:00Z"}, {"id": 2, "user": {"id": 502, "name": "<PERSON>", "avatar": "https://via.placeholder.com/40"}, "content": "The examples were very clear and easy to follow.", "rating": 4, "createdAt": "2024-01-08T09:15:00Z"}], "relatedContent": [{"id": 15, "title": "Advanced React Patterns", "type": "course", "rating": 4.5}, {"id": 16, "title": "React Context API", "type": "article", "rating": 4.3}], "createdAt": "2023-10-15T09:30:00Z", "updatedAt": "2024-01-10T14:20:00Z", "publishedAt": "2023-10-20T10:00:00Z"}