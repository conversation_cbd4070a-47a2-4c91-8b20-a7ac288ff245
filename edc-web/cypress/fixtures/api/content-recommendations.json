{"recommendations": [{"id": 201, "title": "Advanced JavaScript Concepts", "description": "Deep dive into advanced JavaScript features and patterns", "type": "course", "duration": 180, "difficulty": "advanced", "rating": 4.8, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 301, "name": "Expert Developer", "avatar": "https://via.placeholder.com/50"}, "tags": ["javascript", "advanced", "programming"], "recommendationScore": 0.95, "reason": "Based on your interest in JavaScript"}, {"id": 202, "title": "React Best Practices", "description": "Learn the best practices for React development", "type": "article", "readTime": 20, "difficulty": "intermediate", "rating": 4.6, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 302, "name": "React Expert", "avatar": "https://via.placeholder.com/50"}, "tags": ["react", "best-practices", "frontend"], "recommendationScore": 0.88, "reason": "Popular among users with similar interests"}, {"id": 203, "title": "Testing with Cypress", "description": "Complete guide to end-to-end testing with Cypress", "type": "course", "duration": 240, "difficulty": "intermediate", "rating": 4.7, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 303, "name": "Testing Guru", "avatar": "https://via.placeholder.com/50"}, "tags": ["testing", "cypress", "automation"], "recommendationScore": 0.82, "reason": "Trending in your field"}], "metadata": {"algorithm": "collaborative_filtering", "generatedAt": "2024-01-15T12:00:00Z", "userProfile": {"interests": ["javascript", "react", "testing"], "skillLevel": "intermediate", "learningGoals": ["frontend", "automation"]}}}