{"id": 1, "name": "Test Organization", "domain": "test.example.com", "slug": "test-org", "type": "enterprise", "status": "active", "createdAt": "2023-01-15T10:00:00Z", "updatedAt": "2024-01-10T14:30:00Z", "settings": {"branding": {"logo": "https://via.placeholder.com/200x80", "primaryColor": "#007bff", "secondaryColor": "#6c757d", "customCss": ""}, "features": {"contentCreation": true, "socialLearning": true, "analytics": true, "integrations": true, "customBranding": true}, "security": {"ssoRequired": false, "passwordPolicy": {"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": false, "maxAge": 90}, "sessionTimeout": 480, "ipWhitelist": []}, "content": {"allowExternalContent": true, "moderationRequired": false, "allowComments": true, "allowRatings": true, "allowSharing": true}, "notifications": {"email": {"enabled": true, "digest": "weekly", "marketing": false}, "push": {"enabled": true, "achievements": true, "reminders": true}}}, "subscription": {"plan": "enterprise", "status": "active", "userLimit": 10000, "storageLimit": "1TB", "features": ["unlimited_content", "advanced_analytics", "custom_branding", "sso_integration", "api_access"], "expiresAt": "2024-12-31T23:59:59Z"}, "stats": {"totalUsers": 2450, "activeUsers": 1890, "totalContent": 5670, "totalCourses": 234, "completionRate": 78.5, "engagementScore": 85.2}, "admins": [{"id": 1, "email": "<EMAIL>", "name": "Test Admin", "role": "super_admin"}, {"id": 2, "email": "<EMAIL>", "name": "Test Manager", "role": "admin"}], "integrations": {"sso": {"provider": "okta", "configured": true, "lastSync": "2024-01-15T08:00:00Z"}, "lms": {"provider": "moodle", "configured": false}, "analytics": {"provider": "google", "configured": true}}}