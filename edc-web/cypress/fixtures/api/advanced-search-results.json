{"results": [{"id": 101, "title": "Advanced JavaScript Design Patterns", "description": "Deep dive into advanced JavaScript design patterns and architectural concepts", "type": "course", "duration": 240, "difficulty": "advanced", "rating": 4.9, "enrollmentCount": 567, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 301, "name": "<PERSON>", "avatar": "https://via.placeholder.com/50", "title": "Senior Software Architect"}, "tags": ["javascript", "design-patterns", "architecture", "advanced"], "category": "Programming", "createdAt": "2023-09-15T10:00:00Z", "updatedAt": "2024-01-10T14:30:00Z", "relevanceScore": 0.95}, {"id": 102, "title": "Microservices Architecture with Node.js", "description": "Building scalable microservices using Node.js and modern tools", "type": "course", "duration": 300, "difficulty": "advanced", "rating": 4.7, "enrollmentCount": 423, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 302, "name": "<PERSON>", "avatar": "https://via.placeholder.com/50", "title": "DevOps Engineer"}, "tags": ["nodejs", "microservices", "architecture", "scalability"], "category": "Backend Development", "createdAt": "2023-11-20T15:45:00Z", "updatedAt": "2024-01-05T09:20:00Z", "relevanceScore": 0.88}], "pagination": {"currentPage": 1, "totalPages": 3, "totalResults": 23, "resultsPerPage": 10, "hasNextPage": true, "hasPreviousPage": false}, "appliedFilters": {"difficulty": ["advanced"], "type": ["course"], "duration": ["120+"], "category": ["Programming", "Backend Development"]}, "searchMeta": {"query": "advanced javascript architecture", "searchTime": 0.067, "algorithm": "advanced", "boost": {"title": 2.0, "tags": 1.5, "description": 1.0}}}