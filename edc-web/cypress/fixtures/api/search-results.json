{"results": [{"id": 1, "title": "Introduction to React", "description": "Learn the basics of React development", "type": "course", "duration": 120, "difficulty": "beginner", "rating": 4.5, "enrollmentCount": 1250, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 101, "name": "<PERSON>", "avatar": "https://via.placeholder.com/50"}, "tags": ["react", "javascript", "frontend"], "createdAt": "2023-12-01T10:00:00Z", "updatedAt": "2024-01-10T15:30:00Z"}, {"id": 2, "title": "Advanced JavaScript Patterns", "description": "Master advanced JavaScript concepts and patterns", "type": "course", "duration": 180, "difficulty": "advanced", "rating": 4.8, "enrollmentCount": 850, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 102, "name": "<PERSON>", "avatar": "https://via.placeholder.com/50"}, "tags": ["javascript", "patterns", "advanced"], "createdAt": "2023-11-15T14:20:00Z", "updatedAt": "2024-01-05T09:45:00Z"}, {"id": 3, "title": "CSS Grid and Flexbox", "description": "Modern CSS layout techniques", "type": "article", "readTime": 15, "difficulty": "intermediate", "rating": 4.3, "viewCount": 2100, "thumbnail": "https://via.placeholder.com/300x200", "author": {"id": 103, "name": "<PERSON>", "avatar": "https://via.placeholder.com/50"}, "tags": ["css", "layout", "grid", "flexbox"], "createdAt": "2024-01-08T11:15:00Z", "updatedAt": "2024-01-12T16:20:00Z"}], "pagination": {"currentPage": 1, "totalPages": 5, "totalResults": 47, "resultsPerPage": 10, "hasNextPage": true, "hasPreviousPage": false}, "filters": {"applied": {"type": ["course", "article"], "difficulty": ["beginner", "intermediate", "advanced"], "tags": []}, "available": {"types": [{"value": "course", "label": "Courses", "count": 25}, {"value": "article", "label": "Articles", "count": 15}, {"value": "video", "label": "Videos", "count": 7}], "difficulties": [{"value": "beginner", "label": "<PERSON><PERSON><PERSON>", "count": 20}, {"value": "intermediate", "label": "Intermediate", "count": 18}, {"value": "advanced", "label": "Advanced", "count": 9}], "tags": [{"value": "javascript", "label": "JavaScript", "count": 12}, {"value": "react", "label": "React", "count": 8}, {"value": "css", "label": "CSS", "count": 6}, {"value": "frontend", "label": "Frontend", "count": 15}]}}, "searchMeta": {"query": "javascript", "searchTime": 0.045, "suggestions": ["javascript fundamentals", "javascript frameworks", "javascript testing"]}}