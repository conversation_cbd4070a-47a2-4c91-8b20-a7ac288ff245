{"success": true, "message": "Content updated successfully", "content": {"id": 9001, "title": "Updated Test Content", "description": "This content has been updated via API", "type": "article", "status": "published", "author": {"id": 1, "name": "Test User", "email": "<EMAIL>"}, "category": {"id": 1, "name": "Technology"}, "tags": ["updated", "test", "api"], "createdAt": "2024-01-15T12:00:00Z", "updatedAt": "2024-01-15T12:30:00Z"}}