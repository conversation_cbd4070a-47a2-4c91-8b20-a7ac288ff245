{"content": [{"id": 1, "title": "Getting Started with TypeScript", "description": "A comprehensive guide to TypeScript for beginners", "type": "course", "status": "published", "duration": 150, "difficulty": "beginner", "rating": 4.6, "enrollmentCount": 2340, "completionRate": 78, "thumbnail": "https://via.placeholder.com/400x250", "author": {"id": 201, "name": "<PERSON>", "avatar": "https://via.placeholder.com/60", "title": "Senior Developer"}, "category": {"id": 1, "name": "Programming", "slug": "programming"}, "tags": ["typescript", "javascript", "programming"], "skills": [{"id": 1, "name": "TypeScript", "level": "beginner"}, {"id": 2, "name": "JavaScript", "level": "intermediate"}], "createdAt": "2023-10-15T09:30:00Z", "updatedAt": "2024-01-10T14:20:00Z", "publishedAt": "2023-10-20T10:00:00Z"}, {"id": 2, "title": "Modern Web Development Practices", "description": "Best practices for modern web development", "type": "article", "status": "published", "readTime": 25, "difficulty": "intermediate", "rating": 4.4, "viewCount": 5670, "thumbnail": "https://via.placeholder.com/400x250", "author": {"id": 202, "name": "<PERSON>", "avatar": "https://via.placeholder.com/60", "title": "Tech Lead"}, "category": {"id": 2, "name": "Web Development", "slug": "web-development"}, "tags": ["web development", "best practices", "modern"], "skills": [{"id": 3, "name": "Web Development", "level": "intermediate"}, {"id": 4, "name": "Software Architecture", "level": "intermediate"}], "createdAt": "2023-12-05T16:45:00Z", "updatedAt": "2024-01-08T11:30:00Z", "publishedAt": "2023-12-08T09:00:00Z"}, {"id": 3, "title": "Introduction to Machine Learning", "description": "Fundamentals of machine learning and AI", "type": "video", "status": "published", "duration": 45, "difficulty": "beginner", "rating": 4.7, "viewCount": 3420, "thumbnail": "https://via.placeholder.com/400x250", "author": {"id": 203, "name": "Dr. <PERSON>", "avatar": "https://via.placeholder.com/60", "title": "Data Scientist"}, "category": {"id": 3, "name": "Data Science", "slug": "data-science"}, "tags": ["machine learning", "ai", "data science"], "skills": [{"id": 5, "name": "Machine Learning", "level": "beginner"}, {"id": 6, "name": "Python", "level": "beginner"}], "createdAt": "2024-01-02T13:15:00Z", "updatedAt": "2024-01-12T10:45:00Z", "publishedAt": "2024-01-05T14:00:00Z"}], "pagination": {"currentPage": 1, "totalPages": 8, "totalItems": 76, "itemsPerPage": 10, "hasNextPage": true, "hasPreviousPage": false}, "filters": {"categories": [{"id": 1, "name": "Programming", "count": 25}, {"id": 2, "name": "Web Development", "count": 18}, {"id": 3, "name": "Data Science", "count": 12}, {"id": 4, "name": "Design", "count": 21}], "types": [{"value": "course", "label": "Courses", "count": 35}, {"value": "article", "label": "Articles", "count": 28}, {"value": "video", "label": "Videos", "count": 13}], "difficulties": [{"value": "beginner", "label": "<PERSON><PERSON><PERSON>", "count": 30}, {"value": "intermediate", "label": "Intermediate", "count": 32}, {"value": "advanced", "label": "Advanced", "count": 14}]}, "meta": {"totalContentHours": 1250, "averageRating": 4.5, "lastUpdated": "2024-01-15T12:00:00Z"}}