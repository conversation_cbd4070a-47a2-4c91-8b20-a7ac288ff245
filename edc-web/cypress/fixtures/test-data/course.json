{"id": 4001, "title": "Test Course for Cypress", "description": "A comprehensive test course designed for Cypress testing scenarios", "type": "course", "status": "published", "duration": 180, "difficulty": "beginner", "rating": 4.7, "enrollmentCount": 567, "completionRate": 78, "thumbnail": "https://via.placeholder.com/400x250", "instructor": {"id": 5001, "name": "Test Instructor", "avatar": "https://via.placeholder.com/80", "title": "Senior Developer", "bio": "Experienced instructor specializing in web development and testing", "rating": 4.8, "coursesCount": 12, "studentsCount": 5432}, "category": {"id": 2, "name": "Web Development", "slug": "web-development"}, "tags": ["testing", "cypress", "automation", "web-development", "qa"], "skills": [{"id": 10, "name": "Test Automation", "level": "beginner"}, {"id": 11, "name": "Cypress", "level": "intermediate"}, {"id": 12, "name": "JavaScript", "level": "intermediate"}], "prerequisites": [{"id": 100, "title": "JavaScript Fundamentals", "required": true}, {"id": 101, "title": "HTML & CSS Basics", "required": false}], "modules": [{"id": 1, "title": "Introduction to Testing", "duration": 30, "lessons": 5, "completed": false}, {"id": 2, "title": "Getting Started with Cy<PERSON>", "duration": 45, "lessons": 8, "completed": false}, {"id": 3, "title": "Writing Your First Tests", "duration": 60, "lessons": 10, "completed": false}, {"id": 4, "title": "Advanced Testing Patterns", "duration": 45, "lessons": 7, "completed": false}], "pricing": {"type": "free", "originalPrice": 0, "currentPrice": 0, "currency": "USD"}, "features": ["Lifetime access", "Certificate of completion", "Downloadable resources", "Mobile access", "Community support"], "learningOutcomes": ["Understand the fundamentals of test automation", "Write effective Cypress tests", "Implement best practices for testing", "Debug and troubleshoot test issues"], "reviews": [{"id": 1, "user": {"id": 6001, "name": "Student One", "avatar": "https://via.placeholder.com/40"}, "rating": 5, "comment": "Excellent course! Very well structured and easy to follow.", "createdAt": "2024-01-05T14:20:00Z"}, {"id": 2, "user": {"id": 6002, "name": "Student Two", "avatar": "https://via.placeholder.com/40"}, "rating": 4, "comment": "Great content, helped me get started with <PERSON><PERSON> quickly.", "createdAt": "2024-01-03T09:15:00Z"}], "createdAt": "2023-12-01T10:00:00Z", "updatedAt": "2024-01-10T16:45:00Z", "publishedAt": "2023-12-15T09:00:00Z"}