# Cypress Testing Framework Documentation

## Overview

This comprehensive Cypress testing framework provides enterprise-level testing capabilities for the edc-web application, supporting both component testing and end-to-end testing with best practices for maintainability, scalability, and reliability.

## Table of Contents

- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
- [Testing Patterns](#testing-patterns)
- [Custom Commands](#custom-commands)
- [Page Object Models](#page-object-models)
- [Test Data Management](#test-data-management)
- [Running Tests](#running-tests)
- [Best Practices](#best-practices)
- [CI/CD Integration](#cicd-integration)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Node.js 16+ installed
- Cypress 14.2.1+ installed (already included in package.json)
- Application running on `http://127.0.0.1:8080`

### Installation

The testing framework is already configured. To verify installation:

```bash
npm run cy:verify
```

### Quick Start

1. **Start the application:**
   ```bash
   npm run dev
   ```

2. **Open Cypress Test Runner:**
   ```bash
   npm run cy:open
   ```

3. **Run all tests headlessly:**
   ```bash
   npm run test:ci
   ```

## Project Structure

```
cypress/
├── component/                 # Component tests
│   ├── Button/
│   │   └── Button.cy.tsx
│   └── SearchBar/
│       └── SearchBar.cy.tsx
├── e2e/                      # End-to-end tests
│   ├── auth/
│   ├── search/
│   └── smoke/
├── fixtures/                 # Test data files
│   ├── api/
│   ├── auth/
│   └── test-data/
├── support/                  # Support files and utilities
│   ├── commands/            # Custom Cypress commands
│   ├── page-objects/        # Page Object Models
│   ├── utils/              # Test utilities and helpers
│   ├── component.ts        # Component test setup
│   └── e2e.ts             # E2E test setup
├── screenshots/            # Test screenshots
├── videos/                # Test recordings
└── README.md              # This file
```

## Testing Patterns

### Component Testing

Component tests focus on individual React components in isolation:

```typescript
describe('Button Component', () => {
  it('should render with default props', () => {
    cy.mountComponent(<Button>Click me</Button>);
    
    cy.get('button')
      .should('exist')
      .should('contain.text', 'Click me');
  });
});
```

### E2E Testing

End-to-end tests simulate real user workflows:

```typescript
describe('Search Functionality', () => {
  beforeEach(() => {
    cy.loginAsUser();
    cy.visit('/search');
  });

  it('should perform a basic search', () => {
    cy.getByTestId('search-input').type('javascript');
    cy.getByTestId('search-button').click();
    cy.getByTestId('search-results').should('be.visible');
  });
});
```

## Custom Commands

### Authentication Commands

```typescript
cy.login(email, password)           // Login with credentials
cy.loginAsUser()                    // Login as regular user
cy.loginAsAdmin()                   // Login as admin user
cy.logout()                         // Logout current user
```

### UI Interaction Commands

```typescript
cy.getByTestId(testId)              // Get element by data-testid
cy.clickByTestId(testId)            // Click element by data-testid
cy.typeInField(selector, text)      // Type in field with validation
cy.waitForSpinner()                 // Wait for loading spinner
```

### API Commands

```typescript
cy.setupCommonIntercepts()          // Setup common API mocks
cy.mockApiResponse(method, url, response)  // Mock API response
cy.waitForApiCall(alias)            // Wait for API call
```

### Accessibility Commands

```typescript
cy.checkA11y()                      // Run accessibility audit
cy.checkKeyboardNavigation()        // Test keyboard navigation
cy.verifyNoAccessibilityViolations() // Comprehensive a11y check
```

### Data Management Commands

```typescript
cy.seedTestData(type, data)         // Seed test data
cy.clearTestData()                  // Clear test data
cy.createTestUser(userData)         // Create test user
```

## Page Object Models

Page Object Models encapsulate page interactions and provide reusable methods:

```typescript
import { LoginPage } from '../support/page-objects/LoginPage';

const loginPage = new LoginPage();

loginPage
  .visit()
  .enterEmail('<EMAIL>')
  .enterPassword('password')
  .clickLogin()
  .shouldRedirectAfterLogin();
```

### Available Page Objects

- `LoginPage` - Login page interactions
- `SearchPage` - Search functionality
- `DashboardPage` - Dashboard interactions
- `ProfilePage` - User profile management

## Test Data Management

### Fixtures

Test data is organized in the `fixtures/` directory:

```typescript
cy.fixture('auth/user-info.json').then((user) => {
  // Use fixture data
});
```

### Dynamic Test Data

Generate test data dynamically:

```typescript
cy.generateTestData('user', 5).then((users) => {
  // Use generated users
});
```

### Test Data Cleanup

Automatic cleanup after each test:

```typescript
afterEach(() => {
  cy.clearTestData();
});
```

## Running Tests

### Development

```bash
# Open Cypress Test Runner
npm run cy:open

# Open E2E tests
npm run cy:open:e2e

# Open Component tests
npm run cy:open:component
```

### Headless Execution

```bash
# Run all tests
npm run test:ci

# Run E2E tests only
npm run test:e2e

# Run component tests only
npm run test:component

# Run smoke tests
npm run test:smoke
```

### Browser-Specific Testing

```bash
# Run in Chrome
npm run cy:run:chrome

# Run in Firefox
npm run cy:run:firefox

# Run in Edge
npm run cy:run:edge
```

### Responsive Testing

```bash
# Mobile viewport
npm run test:mobile

# Tablet viewport
npm run test:tablet

# Desktop viewport
npm run test:desktop
```

### Environment-Specific Testing

```bash
# Staging environment
npm run test:staging

# Production environment
npm run test:production
```

## Best Practices

### Test Organization

1. **Group related tests** using `describe` blocks
2. **Use descriptive test names** that explain the expected behavior
3. **Follow the AAA pattern**: Arrange, Act, Assert
4. **Keep tests independent** and isolated

### Selectors

1. **Prefer `data-testid` attributes** for test selectors
2. **Avoid CSS selectors** that may change with styling
3. **Use semantic selectors** when possible (roles, labels)

### Test Data

1. **Use fixtures** for static test data
2. **Generate dynamic data** for unique scenarios
3. **Clean up test data** after each test
4. **Avoid hardcoded values** in tests

### Assertions

1. **Be specific** with assertions
2. **Test user-visible behavior** rather than implementation details
3. **Use appropriate timeouts** for async operations
4. **Verify both positive and negative cases**

### Performance

1. **Minimize test execution time** by avoiding unnecessary waits
2. **Use efficient selectors** and commands
3. **Parallelize tests** when possible
4. **Mock external dependencies** to reduce flakiness

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Cypress Tests

on: [push, pull_request]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run Cypress tests
        run: npm run test:ci
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
```

### Jenkins Pipeline

```groovy
pipeline {
    agent any
    
    stages {
        stage('Install') {
            steps {
                sh 'npm ci'
            }
        }
        
        stage('Test') {
            steps {
                sh 'npm run test:ci'
            }
        }
    }
    
    post {
        always {
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'cypress/reports',
                reportFiles: 'index.html',
                reportName: 'Cypress Test Report'
            ])
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Tests timing out**
   - Increase timeout values in cypress.config.ts
   - Check for network issues or slow API responses
   - Verify application is running and accessible

2. **Element not found**
   - Verify selectors are correct
   - Check if element is visible and not hidden
   - Add appropriate waits for dynamic content

3. **Flaky tests**
   - Add proper waits for async operations
   - Mock external dependencies
   - Ensure test isolation and cleanup

4. **Authentication issues**
   - Verify login credentials and API endpoints
   - Check session management and token expiration
   - Ensure proper test data setup

### Debug Mode

Run tests with debug information:

```bash
DEBUG=cypress:* npm run cy:run
```

### Video and Screenshots

Tests automatically capture videos and screenshots on failure. Check the `cypress/videos` and `cypress/screenshots` directories.

## Contributing

1. **Follow naming conventions** for test files and functions
2. **Add documentation** for new custom commands
3. **Update page objects** when UI changes
4. **Write comprehensive tests** for new features
5. **Ensure tests pass** before submitting PRs

## Support

For questions or issues with the testing framework:

1. Check this documentation
2. Review existing test examples
3. Consult the [Cypress documentation](https://docs.cypress.io)
4. Contact the development team
