/**
 * Page Object Model for Login Page
 * 
 * This demonstrates the Page Object pattern for organizing E2E tests
 * with reusable methods and selectors.
 */

export class LoginPage {
  // Selectors
  private selectors = {
    emailInput: '#id-email',
    passwordInput: '#id-password',
    loginButton: '[data-testid="login-button"]',
    forgotPasswordLink: '[data-testid="forgot-password-link"]',
    signUpLink: '[data-testid="sign-up-link"]',
    errorMessage: '[data-testid="login-error"]',
    loadingSpinner: '[data-testid="login-loading"]',
    rememberMeCheckbox: '[data-testid="remember-me"]',
    ssoButton: '[data-testid="sso-login"]',
    loginForm: '[data-testid="login-form"]'
  };

  // Navigation methods
  visit(): void {
    cy.visit('/user/login');
    this.waitForPageLoad();
  }

  waitForPageLoad(): void {
    cy.get(this.selectors.emailInput, { timeout: 10000 }).should('be.visible');
    cy.get(this.selectors.passwordInput).should('be.visible');
    cy.get(this.selectors.loginButton).should('be.visible');
  }

  // Element getters
  getEmailInput() {
    return cy.get(this.selectors.emailInput);
  }

  getPasswordInput() {
    return cy.get(this.selectors.passwordInput);
  }

  getLoginButton() {
    return cy.get(this.selectors.loginButton);
  }

  getForgotPasswordLink() {
    return cy.get(this.selectors.forgotPasswordLink);
  }

  getSignUpLink() {
    return cy.get(this.selectors.signUpLink);
  }

  getErrorMessage() {
    return cy.get(this.selectors.errorMessage);
  }

  getLoadingSpinner() {
    return cy.get(this.selectors.loadingSpinner);
  }

  getRememberMeCheckbox() {
    return cy.get(this.selectors.rememberMeCheckbox);
  }

  getSSOButton() {
    return cy.get(this.selectors.ssoButton);
  }

  getLoginForm() {
    return cy.get(this.selectors.loginForm);
  }

  // Action methods
  enterEmail(email: string): LoginPage {
    this.getEmailInput().clear().type(email);
    return this;
  }

  enterPassword(password: string): LoginPage {
    this.getPasswordInput().clear().type(password);
    return this;
  }

  clickLogin(): LoginPage {
    this.getLoginButton().click();
    return this;
  }

  clickForgotPassword(): void {
    this.getForgotPasswordLink().click();
  }

  clickSignUp(): void {
    this.getSignUpLink().click();
  }

  toggleRememberMe(): LoginPage {
    this.getRememberMeCheckbox().click();
    return this;
  }

  clickSSOLogin(): void {
    this.getSSOButton().click();
  }

  // Compound actions
  login(email: string, password: string, rememberMe = false): void {
    this.enterEmail(email)
        .enterPassword(password);
    
    if (rememberMe) {
      this.toggleRememberMe();
    }
    
    this.clickLogin();
  }

  loginWithValidCredentials(): void {
    this.login('<EMAIL>', 'password123');
  }

  loginWithInvalidCredentials(): void {
    this.login('<EMAIL>', 'wrongpassword');
  }

  // Validation methods
  shouldShowErrorMessage(message?: string): LoginPage {
    this.getErrorMessage().should('be.visible');
    
    if (message) {
      this.getErrorMessage().should('contain.text', message);
    }
    
    return this;
  }

  shouldNotShowErrorMessage(): LoginPage {
    this.getErrorMessage().should('not.exist');
    return this;
  }

  shouldShowLoadingSpinner(): LoginPage {
    this.getLoadingSpinner().should('be.visible');
    return this;
  }

  shouldNotShowLoadingSpinner(): LoginPage {
    this.getLoadingSpinner().should('not.exist');
    return this;
  }

  shouldBeOnLoginPage(): LoginPage {
    cy.url().should('include', '/user/login');
    this.getLoginForm().should('be.visible');
    return this;
  }

  shouldRedirectAfterLogin(expectedUrl = '/'): void {
    cy.url().should('not.include', '/user/login');
    
    if (expectedUrl !== '/') {
      cy.url().should('include', expectedUrl);
    }
  }

  // Form validation methods
  shouldShowEmailValidationError(): LoginPage {
    this.getEmailInput().should('have.class', 'error');
    return this;
  }

  shouldShowPasswordValidationError(): LoginPage {
    this.getPasswordInput().should('have.class', 'error');
    return this;
  }

  shouldHaveDisabledLoginButton(): LoginPage {
    this.getLoginButton().should('be.disabled');
    return this;
  }

  shouldHaveEnabledLoginButton(): LoginPage {
    this.getLoginButton().should('not.be.disabled');
    return this;
  }

  // Accessibility methods
  checkAccessibility(): LoginPage {
    cy.verifyNoAccessibilityViolations();
    return this;
  }

  checkKeyboardNavigation(): LoginPage {
    // Tab through form elements
    cy.get('body').tab();
    this.getEmailInput().should('have.focus');
    
    cy.focused().tab();
    this.getPasswordInput().should('have.focus');
    
    cy.focused().tab();
    this.getRememberMeCheckbox().should('have.focus');
    
    cy.focused().tab();
    this.getLoginButton().should('have.focus');
    
    return this;
  }

  // Test data methods
  fillFormWithTestData(userData: any): LoginPage {
    if (userData.email) {
      this.enterEmail(userData.email);
    }
    
    if (userData.password) {
      this.enterPassword(userData.password);
    }
    
    if (userData.rememberMe) {
      this.toggleRememberMe();
    }
    
    return this;
  }

  // API interaction methods
  setupSuccessfulLoginMock(): LoginPage {
    cy.mockAuthSuccess();
    return this;
  }

  setupFailedLoginMock(): LoginPage {
    cy.mockAuthFailure();
    return this;
  }

  waitForLoginRequest(): LoginPage {
    cy.waitForApiCall('loginRequest');
    return this;
  }

  // Responsive testing methods
  testMobileLayout(): LoginPage {
    cy.viewport('iphone-x');
    this.getLoginForm().should('be.visible');
    this.getEmailInput().should('be.visible');
    this.getPasswordInput().should('be.visible');
    this.getLoginButton().should('be.visible');
    return this;
  }

  testTabletLayout(): LoginPage {
    cy.viewport('ipad-2');
    this.getLoginForm().should('be.visible');
    return this;
  }

  testDesktopLayout(): LoginPage {
    cy.viewport(1280, 720);
    this.getLoginForm().should('be.visible');
    return this;
  }

  // Performance testing methods
  measureLoginTime(): Cypress.Chainable<number> {
    const startTime = Date.now();
    
    this.clickLogin();
    
    return cy.url().should('not.include', '/user/login').then(() => {
      return Date.now() - startTime;
    });
  }

  // Security testing methods
  testPasswordMasking(): LoginPage {
    this.getPasswordInput().should('have.attr', 'type', 'password');
    return this;
  }

  testFormSubmissionPrevention(): LoginPage {
    // Test that form doesn't submit with empty fields
    this.clickLogin();
    this.shouldBeOnLoginPage();
    return this;
  }
}
