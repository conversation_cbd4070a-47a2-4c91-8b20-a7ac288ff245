/**
 * Page Object Model for Search Page
 * 
 * Encapsulates search page interactions and validations
 */

export class SearchPage {
  // Selectors
  private selectors = {
    searchInput: '[data-testid="search-input"]',
    searchButton: '[data-testid="search-button"]',
    searchSuggestions: '[data-testid="search-suggestions"]',
    searchResults: '[data-testid="search-results"]',
    resultsCount: '[data-testid="results-count"]',
    noResults: '[data-testid="no-results"]',
    loadingSpinner: '[data-testid="search-loading"]',
    errorMessage: '[data-testid="search-error"]',
    retryButton: '[data-testid="retry-search-button"]',
    
    // Filters
    filtersPanel: '[data-testid="search-filters"]',
    contentTypeFilter: '[data-testid="filter-content-type"]',
    difficultyFilter: '[data-testid="filter-difficulty"]',
    applyFiltersButton: '[data-testid="apply-filters-button"]',
    clearFiltersButton: '[data-testid="clear-filters-button"]',
    
    // Pagination
    pagination: '[data-testid="pagination"]',
    paginationNext: '[data-testid="pagination-next"]',
    paginationPrev: '[data-testid="pagination-prev"]',
    
    // Sort options
    sortDropdown: '[data-testid="sort-dropdown"]',
    sortRelevance: '[data-testid="sort-relevance"]',
    sortDate: '[data-testid="sort-date"]',
    sortRating: '[data-testid="sort-rating"]'
  };

  // Navigation
  visit(query?: string): void {
    const url = query ? `/search?q=${encodeURIComponent(query)}` : '/search';
    cy.visit(url);
    this.waitForPageLoad();
  }

  waitForPageLoad(): void {
    cy.get(this.selectors.searchInput, { timeout: 10000 }).should('be.visible');
  }

  // Element getters
  getSearchInput() {
    return cy.get(this.selectors.searchInput);
  }

  getSearchButton() {
    return cy.get(this.selectors.searchButton);
  }

  getSearchSuggestions() {
    return cy.get(this.selectors.searchSuggestions);
  }

  getSearchResults() {
    return cy.get(this.selectors.searchResults);
  }

  getResultsCount() {
    return cy.get(this.selectors.resultsCount);
  }

  getNoResultsMessage() {
    return cy.get(this.selectors.noResults);
  }

  getLoadingSpinner() {
    return cy.get(this.selectors.loadingSpinner);
  }

  getErrorMessage() {
    return cy.get(this.selectors.errorMessage);
  }

  getRetryButton() {
    return cy.get(this.selectors.retryButton);
  }

  getFiltersPanel() {
    return cy.get(this.selectors.filtersPanel);
  }

  getPagination() {
    return cy.get(this.selectors.pagination);
  }

  getSortDropdown() {
    return cy.get(this.selectors.sortDropdown);
  }

  // Search actions
  enterSearchQuery(query: string): SearchPage {
    this.getSearchInput().clear().type(query);
    return this;
  }

  clickSearchButton(): SearchPage {
    this.getSearchButton().click();
    return this;
  }

  performSearch(query: string): SearchPage {
    this.enterSearchQuery(query);
    this.clickSearchButton();
    return this;
  }

  clearSearch(): SearchPage {
    this.getSearchInput().clear();
    return this;
  }

  // Suggestions
  waitForSuggestions(): SearchPage {
    this.getSearchSuggestions().should('be.visible');
    return this;
  }

  clickSuggestion(index: number): SearchPage {
    cy.get(`[data-testid="suggestion-${index}"]`).click();
    return this;
  }

  selectFirstSuggestion(): SearchPage {
    return this.clickSuggestion(0);
  }

  // Results validation
  shouldShowResults(): SearchPage {
    this.getSearchResults().should('be.visible');
    return this;
  }

  shouldShowNoResults(): SearchPage {
    this.getNoResultsMessage().should('be.visible');
    return this;
  }

  shouldShowResultsCount(count?: number): SearchPage {
    this.getResultsCount().should('be.visible');
    
    if (count !== undefined) {
      this.getResultsCount().should('contain', count.toString());
    }
    
    return this;
  }

  shouldShowLoadingSpinner(): SearchPage {
    this.getLoadingSpinner().should('be.visible');
    return this;
  }

  shouldNotShowLoadingSpinner(): SearchPage {
    this.getLoadingSpinner().should('not.exist');
    return this;
  }

  shouldShowErrorMessage(message?: string): SearchPage {
    this.getErrorMessage().should('be.visible');
    
    if (message) {
      this.getErrorMessage().should('contain.text', message);
    }
    
    return this;
  }

  // Result interactions
  clickResult(index: number): void {
    cy.get(`[data-testid="result-${index}"]`).click();
  }

  clickFirstResult(): void {
    this.clickResult(0);
  }

  getResult(index: number) {
    return cy.get(`[data-testid="result-${index}"]`);
  }

  getResultTitle(index: number) {
    return this.getResult(index).find('[data-testid="result-title"]');
  }

  getResultDescription(index: number) {
    return this.getResult(index).find('[data-testid="result-description"]');
  }

  getResultAuthor(index: number) {
    return this.getResult(index).find('[data-testid="result-author"]');
  }

  getResultRating(index: number) {
    return this.getResult(index).find('[data-testid="result-rating"]');
  }

  // Filters
  openFiltersPanel(): SearchPage {
    this.getFiltersPanel().should('be.visible');
    return this;
  }

  selectContentTypeFilter(type: string): SearchPage {
    cy.get(this.selectors.contentTypeFilter).click();
    cy.get(`[data-testid="filter-option-${type}"]`).click();
    return this;
  }

  selectDifficultyFilter(difficulty: string): SearchPage {
    cy.get(this.selectors.difficultyFilter).click();
    cy.get(`[data-testid="filter-option-${difficulty}"]`).click();
    return this;
  }

  applyFilters(): SearchPage {
    cy.get(this.selectors.applyFiltersButton).click();
    return this;
  }

  clearFilters(): SearchPage {
    cy.get(this.selectors.clearFiltersButton).click();
    return this;
  }

  // Pagination
  goToNextPage(): SearchPage {
    cy.get(this.selectors.paginationNext).click();
    return this;
  }

  goToPreviousPage(): SearchPage {
    cy.get(this.selectors.paginationPrev).click();
    return this;
  }

  goToPage(pageNumber: number): SearchPage {
    cy.get(`[data-testid="pagination-page-${pageNumber}"]`).click();
    return this;
  }

  // Sorting
  sortBy(option: 'relevance' | 'date' | 'rating'): SearchPage {
    this.getSortDropdown().click();
    
    switch (option) {
      case 'relevance':
        cy.get(this.selectors.sortRelevance).click();
        break;
      case 'date':
        cy.get(this.selectors.sortDate).click();
        break;
      case 'rating':
        cy.get(this.selectors.sortRating).click();
        break;
    }
    
    return this;
  }

  // Validation methods
  shouldHaveResultsInOrder(order: 'asc' | 'desc', field: string): SearchPage {
    cy.get('[data-testid^="result-"]').then($results => {
      const values = Array.from($results).map(result => {
        const element = Cypress.$(result).find(`[data-testid="result-${field}"]`);
        return element.text();
      });
      
      const sortedValues = [...values].sort();
      if (order === 'desc') {
        sortedValues.reverse();
      }
      
      expect(values).to.deep.equal(sortedValues);
    });
    
    return this;
  }

  shouldHaveUrlParameter(param: string, value: string): SearchPage {
    cy.url().should('include', `${param}=${value}`);
    return this;
  }

  shouldNotHaveUrlParameter(param: string): SearchPage {
    cy.url().should('not.include', `${param}=`);
    return this;
  }

  // Keyboard navigation
  navigateWithKeyboard(): SearchPage {
    // Tab to search input
    cy.get('body').tab();
    this.getSearchInput().should('have.focus');
    
    // Tab to search button
    cy.focused().tab();
    this.getSearchButton().should('have.focus');
    
    return this;
  }

  navigateSuggestionsWithKeyboard(): SearchPage {
    // Use arrow keys to navigate suggestions
    this.getSearchInput().type('{downarrow}');
    cy.get('[data-testid="suggestion-0"]').should('have.class', 'selected');
    
    this.getSearchInput().type('{downarrow}');
    cy.get('[data-testid="suggestion-1"]').should('have.class', 'selected');
    
    this.getSearchInput().type('{uparrow}');
    cy.get('[data-testid="suggestion-0"]').should('have.class', 'selected');
    
    return this;
  }

  // Performance testing
  measureSearchTime(): Cypress.Chainable<number> {
    const startTime = Date.now();
    
    this.clickSearchButton();
    
    return this.getSearchResults().should('be.visible').then(() => {
      return Date.now() - startTime;
    });
  }

  // Accessibility testing
  checkAccessibility(): SearchPage {
    cy.verifyNoAccessibilityViolations();
    return this;
  }

  checkSearchInputAccessibility(): SearchPage {
    this.getSearchInput()
      .should('have.attr', 'aria-label')
      .should('have.attr', 'role');
    
    return this;
  }

  checkResultsAccessibility(): SearchPage {
    this.getSearchResults()
      .should('have.attr', 'role', 'region')
      .should('have.attr', 'aria-label');
    
    return this;
  }

  // Mobile testing
  testMobileLayout(): SearchPage {
    cy.viewport('iphone-x');
    
    this.getSearchInput().should('be.visible');
    this.getSearchButton().should('be.visible');
    
    return this;
  }

  // API interaction helpers
  waitForSearchResults(): SearchPage {
    cy.waitForApiCall('searchResults');
    return this;
  }

  waitForSearchSuggestions(): SearchPage {
    cy.waitForApiCall('searchSuggestions');
    return this;
  }

  setupSearchMocks(): SearchPage {
    cy.interceptSearchApi();
    return this;
  }
}
