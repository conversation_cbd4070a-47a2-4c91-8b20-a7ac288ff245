/**
 * UI interaction custom commands for Cypress tests
 */

declare global {
  namespace Cypress {
    interface Chainable {
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
      findByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
      clickByTestId(testId: string): Chainable<void>;
      typeInField(selector: string, text: string, options?: any): Chainable<void>;
      selectDropdownOption(dropdownSelector: string, optionText: string): Chainable<void>;
      uploadFile(selector: string, fileName: string, fileType?: string): Chainable<void>;
      waitForSpinner(): Chainable<void>;
      waitForPageLoad(): Chainable<void>;
      scrollToElement(selector: string): Chainable<void>;
      hoverAndClick(selector: string): Chainable<void>;
      dragAndDrop(sourceSelector: string, targetSelector: string): Chainable<void>;
      checkResponsive(breakpoints?: string[]): Chainable<void>;
    }
  }
}

/**
 * Get element by data-testid attribute
 */
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`);
});

/**
 * Find element by data-testid attribute within current subject
 */
Cypress.Commands.add('findByTestId', { prevSubject: 'element' }, (subject, testId: string) => {
  return cy.wrap(subject).find(`[data-testid="${testId}"]`);
});

/**
 * Click element by data-testid
 */
Cypress.Commands.add('clickByTestId', (testId: string) => {
  cy.getByTestId(testId).click();
});

/**
 * Type in field with better error handling
 */
Cypress.Commands.add('typeInField', (selector: string, text: string, options = {}) => {
  cy.get(selector)
    .should('be.visible')
    .clear()
    .type(text, { delay: 50, ...options });
});

/**
 * Select dropdown option by text
 */
Cypress.Commands.add('selectDropdownOption', (dropdownSelector: string, optionText: string) => {
  cy.get(dropdownSelector).click();
  cy.get('.dropdown-menu, .select-menu, [role="listbox"]')
    .should('be.visible')
    .contains(optionText)
    .click();
});

/**
 * Upload file to input
 */
Cypress.Commands.add('uploadFile', (selector: string, fileName: string, fileType = 'image/png') => {
  cy.fixture(fileName, 'base64').then(fileContent => {
    cy.get(selector).selectFile({
      contents: Cypress.Buffer.from(fileContent, 'base64'),
      fileName,
      mimeType: fileType,
    }, { force: true });
  });
});

/**
 * Wait for loading spinner to disappear
 */
Cypress.Commands.add('waitForSpinner', () => {
  // Wait for spinner to appear (optional)
  cy.get('.spinner, .loading, [data-testid="loading"]', { timeout: 1000 }).should('exist').then(() => {
    // Wait for spinner to disappear
    cy.get('.spinner, .loading, [data-testid="loading"]', { timeout: 15000 }).should('not.exist');
  }).catch(() => {
    // If spinner never appears, that's fine too
    cy.log('No spinner found, continuing...');
  });
});

/**
 * Wait for page to fully load
 */
Cypress.Commands.add('waitForPageLoad', () => {
  // Wait for document ready state
  cy.window().its('document.readyState').should('equal', 'complete');
  
  // Wait for any loading indicators to disappear
  cy.waitForSpinner();
  
  // Wait for main content to be visible
  cy.get('main, [role="main"], .main-content', { timeout: 10000 }).should('be.visible');
});

/**
 * Scroll element into view
 */
Cypress.Commands.add('scrollToElement', (selector: string) => {
  cy.get(selector).scrollIntoView({ duration: 500 });
});

/**
 * Hover over element and click
 */
Cypress.Commands.add('hoverAndClick', (selector: string) => {
  cy.get(selector).trigger('mouseover').click();
});

/**
 * Drag and drop functionality
 */
Cypress.Commands.add('dragAndDrop', (sourceSelector: string, targetSelector: string) => {
  cy.get(sourceSelector).trigger('mousedown', { button: 0 });
  cy.get(targetSelector).trigger('mousemove').trigger('mouseup');
});

/**
 * Test responsive design at different breakpoints
 */
Cypress.Commands.add('checkResponsive', (breakpoints = ['mobile', 'tablet', 'desktop']) => {
  const viewports = {
    mobile: [375, 667],
    tablet: [768, 1024],
    desktop: [1280, 720]
  };

  breakpoints.forEach(breakpoint => {
    if (viewports[breakpoint]) {
      const [width, height] = viewports[breakpoint];
      cy.viewport(width, height);
      cy.wait(500); // Allow time for responsive changes
      
      // Take screenshot for visual comparison
      cy.screenshot(`responsive-${breakpoint}`);
    }
  });
});
