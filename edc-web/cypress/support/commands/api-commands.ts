/**
 * API-related custom commands for Cypress tests
 */

declare global {
  namespace Cypress {
    interface Chainable {
      setupCommonIntercepts(): Chainable<void>;
      mockApiResponse(method: string, url: string, response: any, alias?: string): Chainable<void>;
      mockApiError(method: string, url: string, statusCode?: number, alias?: string): Chainable<void>;
      waitForApiCall(alias: string, timeout?: number): Chainable<void>;
      interceptSearchApi(): Chainable<void>;
      interceptContentApi(): Chainable<void>;
      interceptUserApi(): Chainable<void>;
      mockLoadingState(alias: string, delay?: number): Chainable<void>;
    }
  }
}

/**
 * Setup common API intercepts used across tests
 */
Cypress.Commands.add('setupCommonIntercepts', () => {
  // User info endpoint
  cy.intercept('GET', '/api/users/info.json*', { fixture: 'api/user-info.json' }).as('userInfo');
  
  // Configuration endpoints
  cy.intercept('GET', '/api/config*', { fixture: 'api/config.json' }).as('config');
  
  // Team/organization info
  cy.intercept('GET', '/api/teams/current*', { fixture: 'api/team-info.json' }).as('teamInfo');
  
  // Feature flags
  cy.intercept('GET', '/api/feature_flags*', { fixture: 'api/feature-flags.json' }).as('featureFlags');
  
  // Translation files
  cy.intercept('GET', '**/translations/**/*.json', { fixture: 'translations/common.json' }).as('translations');
  
  // Health check
  cy.intercept('GET', '/api/health', { statusCode: 200, body: { status: 'ok' } }).as('healthCheck');
});

/**
 * Mock API response with custom data
 */
Cypress.Commands.add('mockApiResponse', (method: string, url: string, response: any, alias?: string) => {
  const interceptAlias = alias || `api${Date.now()}`;
  
  cy.intercept(method.toUpperCase(), url, {
    statusCode: 200,
    body: response
  }).as(interceptAlias);
  
  return cy.wrap(interceptAlias);
});

/**
 * Mock API error response
 */
Cypress.Commands.add('mockApiError', (method: string, url: string, statusCode = 500, alias?: string) => {
  const interceptAlias = alias || `apiError${Date.now()}`;
  
  cy.intercept(method.toUpperCase(), url, {
    statusCode,
    body: {
      error: 'API Error',
      message: `Mock error response with status ${statusCode}`
    }
  }).as(interceptAlias);
  
  return cy.wrap(interceptAlias);
});

/**
 * Wait for API call with timeout
 */
Cypress.Commands.add('waitForApiCall', (alias: string, timeout = 10000) => {
  cy.wait(`@${alias}`, { timeout });
});

/**
 * Setup search API intercepts
 */
Cypress.Commands.add('interceptSearchApi', () => {
  // Search endpoint
  cy.intercept('GET', '/api/search*', { fixture: 'api/search-results.json' }).as('searchResults');
  
  // Search suggestions
  cy.intercept('GET', '/api/search/suggestions*', { fixture: 'api/search-suggestions.json' }).as('searchSuggestions');
  
  // Search filters
  cy.intercept('GET', '/api/search/filters*', { fixture: 'api/search-filters.json' }).as('searchFilters');
  
  // Advanced search
  cy.intercept('POST', '/api/search/advanced*', { fixture: 'api/advanced-search-results.json' }).as('advancedSearch');
});

/**
 * Setup content API intercepts
 */
Cypress.Commands.add('interceptContentApi', () => {
  // Content list
  cy.intercept('GET', '/api/content*', { fixture: 'api/content-list.json' }).as('contentList');
  
  // Content details
  cy.intercept('GET', '/api/content/*/details*', { fixture: 'api/content-details.json' }).as('contentDetails');
  
  // Content creation
  cy.intercept('POST', '/api/content*', { fixture: 'api/content-created.json' }).as('contentCreate');
  
  // Content update
  cy.intercept('PUT', '/api/content/*', { fixture: 'api/content-updated.json' }).as('contentUpdate');
  
  // Content deletion
  cy.intercept('DELETE', '/api/content/*', { statusCode: 204 }).as('contentDelete');
  
  // Content recommendations
  cy.intercept('GET', '/api/content/recommendations*', { fixture: 'api/content-recommendations.json' }).as('contentRecommendations');
});

/**
 * Setup user-related API intercepts
 */
Cypress.Commands.add('interceptUserApi', () => {
  // User profile
  cy.intercept('GET', '/api/users/*/profile*', { fixture: 'api/user-profile.json' }).as('userProfile');
  
  // User preferences
  cy.intercept('GET', '/api/users/*/preferences*', { fixture: 'api/user-preferences.json' }).as('userPreferences');
  
  // Update user preferences
  cy.intercept('PUT', '/api/users/*/preferences*', { fixture: 'api/user-preferences-updated.json' }).as('updateUserPreferences');
  
  // User activity
  cy.intercept('GET', '/api/users/*/activity*', { fixture: 'api/user-activity.json' }).as('userActivity');
  
  // User notifications
  cy.intercept('GET', '/api/users/*/notifications*', { fixture: 'api/user-notifications.json' }).as('userNotifications');
});

/**
 * Mock loading state with delay
 */
Cypress.Commands.add('mockLoadingState', (alias: string, delay = 2000) => {
  cy.intercept('GET', `**/${alias}**`, (req) => {
    req.reply((res) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(res.send({ fixture: `api/${alias}.json` }));
        }, delay);
      });
    });
  }).as(`${alias}Loading`);
});
