/**
 * Authentication-related custom commands for Cypress tests
 */

declare global {
  namespace Cypress {
    interface Chainable {
      login(email?: string, password?: string): Chainable<void>;
      loginAsAdmin(): Chainable<void>;
      loginAsUser(): Chainable<void>;
      logout(): Chainable<void>;
      setupAuthIntercepts(): Chainable<void>;
      mockAuthSuccess(): Chainable<void>;
      mockAuthFailure(): Chainable<void>;
    }
  }
}

// Default test credentials
const DEFAULT_CREDENTIALS = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123'
  },
  user: {
    email: '<EMAIL>',
    password: 'user123'
  }
};

/**
 * Login command with email and password
 */
Cypress.Commands.add('login', (email?: string, password?: string) => {
  const credentials = {
    email: email || DEFAULT_CREDENTIALS.user.email,
    password: password || DEFAULT_CREDENTIALS.user.password
  };

  cy.session([credentials.email, credentials.password], () => {
    cy.visit('/user/login');
    
    // Wait for login form to be visible
    cy.get('#id-email', { timeout: 10000 }).should('be.visible');
    cy.get('#id-password').should('be.visible');
    
    // Fill in credentials
    cy.get('#id-email').clear().type(credentials.email);
    cy.get('#id-password').clear().type(credentials.password);
    
    // Submit form
    cy.get('form').submit();
    
    // Wait for successful login redirect
    cy.url().should('not.include', '/user/login');
    cy.get('[data-testid="user-menu"]', { timeout: 15000 }).should('exist');
  });
});

/**
 * Login as admin user
 */
Cypress.Commands.add('loginAsAdmin', () => {
  cy.login(DEFAULT_CREDENTIALS.admin.email, DEFAULT_CREDENTIALS.admin.password);
});

/**
 * Login as regular user
 */
Cypress.Commands.add('loginAsUser', () => {
  cy.login(DEFAULT_CREDENTIALS.user.email, DEFAULT_CREDENTIALS.user.password);
});

/**
 * Logout command
 */
Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('include', '/user/login');
});

/**
 * Setup authentication-related intercepts
 */
Cypress.Commands.add('setupAuthIntercepts', () => {
  // Intercept login API call
  cy.intercept('POST', '/auth/sign_in.json', { fixture: 'auth/login-success.json' }).as('loginRequest');
  
  // Intercept user info API call
  cy.intercept('GET', '/api/users/info.json*', { fixture: 'auth/user-info.json' }).as('userInfoRequest');
  
  // Intercept logout API call
  cy.intercept('DELETE', '/auth/sign_out.json', { statusCode: 200 }).as('logoutRequest');
  
  // Intercept session validation
  cy.intercept('GET', '/api/users/validate_session.json', { fixture: 'auth/session-valid.json' }).as('sessionValidation');
});

/**
 * Mock successful authentication
 */
Cypress.Commands.add('mockAuthSuccess', () => {
  cy.intercept('POST', '/auth/sign_in.json', {
    statusCode: 200,
    body: {
      success: true,
      user: {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user'
      },
      token: 'mock-jwt-token'
    }
  }).as('mockLoginSuccess');
  
  cy.intercept('GET', '/api/users/info.json*', {
    statusCode: 200,
    body: {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      isLoggedIn: true
    }
  }).as('mockUserInfo');
});

/**
 * Mock authentication failure
 */
Cypress.Commands.add('mockAuthFailure', () => {
  cy.intercept('POST', '/auth/sign_in.json', {
    statusCode: 401,
    body: {
      success: false,
      error: 'Invalid credentials'
    }
  }).as('mockLoginFailure');
  
  cy.intercept('GET', '/api/users/info.json*', {
    statusCode: 401,
    body: {
      error: 'Unauthorized'
    }
  }).as('mockUserInfoUnauthorized');
});
