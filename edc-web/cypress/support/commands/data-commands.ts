/**
 * Data management custom commands for Cypress tests
 */

declare global {
  namespace Cypress {
    interface Chainable {
      seedTestData(dataType: string, data?: any): Chainable<void>;
      clearTestData(dataType?: string): Chainable<void>;
      createTestUser(userData?: any): Chainable<any>;
      createTestContent(contentData?: any): Chainable<any>;
      createTestCourse(courseData?: any): Chainable<any>;
      setupTestEnvironment(): Chainable<void>;
      resetTestEnvironment(): Chainable<void>;
      loadFixtureData(fixtureName: string): Chainable<any>;
      generateTestData(type: string, count?: number): Chainable<any>;
    }
  }
}

/**
 * Seed test data into the system
 */
Cypress.Commands.add('seedTestData', (dataType: string, data = {}) => {
  cy.task('seedDatabase', { type: dataType, data });
  cy.log(`✅ Seeded test data for: ${dataType}`);
});

/**
 * Clear test data from the system
 */
Cypress.Commands.add('clearTestData', (dataType?: string) => {
  cy.task('clearTestData', dataType ? { type: dataType } : null);
  cy.log(`🧹 Cleared test data${dataType ? ` for: ${dataType}` : ''}`);
});

/**
 * Create a test user with default or custom data
 */
Cypress.Commands.add('createTestUser', (userData = {}) => {
  const defaultUserData = {
    email: `test.user.${Date.now()}@example.com`,
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    isActive: true,
    ...userData
  };

  return cy.request({
    method: 'POST',
    url: '/api/test/users',
    body: defaultUserData,
    failOnStatusCode: false
  }).then((response) => {
    if (response.status === 201) {
      cy.log(`✅ Created test user: ${defaultUserData.email}`);
      return cy.wrap(response.body);
    } else {
      // Fallback to fixture data if API not available
      cy.fixture('test-data/user.json').then((fixtureUser) => {
        const testUser = { ...fixtureUser, ...defaultUserData };
        cy.log(`📁 Using fixture test user: ${testUser.email}`);
        return cy.wrap(testUser);
      });
    }
  });
});

/**
 * Create test content with default or custom data
 */
Cypress.Commands.add('createTestContent', (contentData = {}) => {
  const defaultContentData = {
    title: `Test Content ${Date.now()}`,
    description: 'This is test content created by Cypress',
    type: 'article',
    status: 'published',
    tags: ['test', 'cypress'],
    ...contentData
  };

  return cy.request({
    method: 'POST',
    url: '/api/test/content',
    body: defaultContentData,
    failOnStatusCode: false
  }).then((response) => {
    if (response.status === 201) {
      cy.log(`✅ Created test content: ${defaultContentData.title}`);
      return cy.wrap(response.body);
    } else {
      // Fallback to fixture data
      cy.fixture('test-data/content.json').then((fixtureContent) => {
        const testContent = { ...fixtureContent, ...defaultContentData };
        cy.log(`📁 Using fixture test content: ${testContent.title}`);
        return cy.wrap(testContent);
      });
    }
  });
});

/**
 * Create test course with default or custom data
 */
Cypress.Commands.add('createTestCourse', (courseData = {}) => {
  const defaultCourseData = {
    title: `Test Course ${Date.now()}`,
    description: 'This is a test course created by Cypress',
    duration: 60,
    difficulty: 'beginner',
    category: 'technology',
    isPublished: true,
    ...courseData
  };

  return cy.request({
    method: 'POST',
    url: '/api/test/courses',
    body: defaultCourseData,
    failOnStatusCode: false
  }).then((response) => {
    if (response.status === 201) {
      cy.log(`✅ Created test course: ${defaultCourseData.title}`);
      return cy.wrap(response.body);
    } else {
      // Fallback to fixture data
      cy.fixture('test-data/course.json').then((fixtureCourse) => {
        const testCourse = { ...fixtureCourse, ...defaultCourseData };
        cy.log(`📁 Using fixture test course: ${testCourse.title}`);
        return cy.wrap(testCourse);
      });
    }
  });
});

/**
 * Setup complete test environment
 */
Cypress.Commands.add('setupTestEnvironment', () => {
  cy.log('🚀 Setting up test environment...');
  
  // Clear any existing test data
  cy.clearTestData();
  
  // Setup common intercepts
  cy.setupCommonIntercepts();
  
  // Create base test data
  cy.createTestUser({ role: 'admin', email: '<EMAIL>' });
  cy.createTestUser({ role: 'user', email: '<EMAIL>' });
  
  // Setup authentication mocks
  cy.mockAuthSuccess();
  
  cy.log('✅ Test environment setup complete');
});

/**
 * Reset test environment to clean state
 */
Cypress.Commands.add('resetTestEnvironment', () => {
  cy.log('🔄 Resetting test environment...');
  
  // Clear all test data
  cy.clearTestData();
  
  // Clear browser state
  cy.clearLocalStorage();
  cy.clearCookies();
  
  // Reset any mocked APIs
  cy.window().then((win) => {
    if (win.fetch && win.fetch.restore) {
      win.fetch.restore();
    }
  });
  
  cy.log('✅ Test environment reset complete');
});

/**
 * Load fixture data with error handling
 */
Cypress.Commands.add('loadFixtureData', (fixtureName: string) => {
  return cy.fixture(fixtureName).then((data) => {
    cy.log(`📁 Loaded fixture data: ${fixtureName}`);
    return cy.wrap(data);
  }).catch((error) => {
    cy.log(`❌ Failed to load fixture: ${fixtureName}`);
    throw error;
  });
});

/**
 * Generate test data dynamically
 */
Cypress.Commands.add('generateTestData', (type: string, count = 1) => {
  const generators = {
    user: () => ({
      id: Math.floor(Math.random() * 10000),
      email: `user${Date.now()}@example.com`,
      firstName: 'Test',
      lastName: 'User',
      role: 'user',
      createdAt: new Date().toISOString()
    }),
    
    content: () => ({
      id: Math.floor(Math.random() * 10000),
      title: `Test Content ${Date.now()}`,
      description: 'Generated test content',
      type: 'article',
      status: 'published',
      createdAt: new Date().toISOString()
    }),
    
    course: () => ({
      id: Math.floor(Math.random() * 10000),
      title: `Test Course ${Date.now()}`,
      description: 'Generated test course',
      duration: Math.floor(Math.random() * 120) + 30,
      difficulty: ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)],
      createdAt: new Date().toISOString()
    })
  };

  const generator = generators[type];
  if (!generator) {
    throw new Error(`Unknown test data type: ${type}`);
  }

  const data = Array.from({ length: count }, () => generator());
  cy.log(`🎲 Generated ${count} ${type} test data items`);
  
  return cy.wrap(count === 1 ? data[0] : data);
});
