/**
 * Accessibility testing custom commands for Cypress tests
 */

declare global {
  namespace Cypress {
    interface Chainable {
      checkA11y(context?: string, options?: any): Chainable<void>;
      checkColorContrast(selector?: string): Chainable<void>;
      checkKeyboardNavigation(startSelector?: string): Chainable<void>;
      checkAriaLabels(selector?: string): Chainable<void>;
      checkFocusManagement(): Chainable<void>;
      checkScreenReaderContent(): Chainable<void>;
      simulateScreenReader(selector: string): Chainable<void>;
      checkTabOrder(selectors: string[]): Chainable<void>;
      verifyNoAccessibilityViolations(): Chainable<void>;
    }
  }
}

/**
 * Run accessibility audit using axe-core
 * Note: This requires cypress-axe plugin to be installed
 */
Cypress.Commands.add('checkA11y', (context?: string, options = {}) => {
  const defaultOptions = {
    includedImpacts: ['critical', 'serious'],
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true },
      'aria-labels': { enabled: true }
    },
    ...options
  };

  // If axe is available, use it
  if (Cypress.env('axe_enabled')) {
    cy.checkA11y(context, defaultOptions);
  } else {
    // Fallback to manual accessibility checks
    cy.checkAriaLabels(context);
    cy.checkKeyboardNavigation();
    cy.checkFocusManagement();
  }
});

/**
 * Check color contrast ratios
 */
Cypress.Commands.add('checkColorContrast', (selector = 'body') => {
  cy.get(selector).within(() => {
    // Check text elements for sufficient contrast
    cy.get('p, h1, h2, h3, h4, h5, h6, span, a, button, label').each(($el) => {
      const element = $el[0];
      const styles = window.getComputedStyle(element);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      // Log color information for manual review
      cy.log(`Element: ${element.tagName}, Color: ${color}, Background: ${backgroundColor}`);
      
      // Basic check - ensure text is not transparent
      expect(color).to.not.equal('rgba(0, 0, 0, 0)');
      expect(color).to.not.equal('transparent');
    });
  });
});

/**
 * Test keyboard navigation
 */
Cypress.Commands.add('checkKeyboardNavigation', (startSelector = 'body') => {
  cy.get(startSelector).within(() => {
    // Find all focusable elements
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    cy.get(focusableSelectors).then($elements => {
      if ($elements.length > 0) {
        // Test Tab navigation
        cy.get($elements[0]).focus();
        
        for (let i = 1; i < Math.min($elements.length, 10); i++) {
          cy.focused().tab();
          cy.focused().should('exist');
        }
        
        // Test Shift+Tab navigation
        for (let i = 0; i < Math.min($elements.length - 1, 5); i++) {
          cy.focused().tab({ shift: true });
          cy.focused().should('exist');
        }
      }
    });
  });
});

/**
 * Check ARIA labels and attributes
 */
Cypress.Commands.add('checkAriaLabels', (selector = 'body') => {
  cy.get(selector).within(() => {
    // Check buttons have accessible names
    cy.get('button').each(($button) => {
      const button = $button[0];
      const hasAriaLabel = button.hasAttribute('aria-label');
      const hasAriaLabelledBy = button.hasAttribute('aria-labelledby');
      const hasTextContent = button.textContent?.trim();
      
      expect(hasAriaLabel || hasAriaLabelledBy || hasTextContent).to.be.true;
    });
    
    // Check form inputs have labels
    cy.get('input, select, textarea').each(($input) => {
      const input = $input[0];
      const hasLabel = input.hasAttribute('aria-label') || 
                      input.hasAttribute('aria-labelledby') ||
                      document.querySelector(`label[for="${input.id}"]`);
      
      expect(hasLabel).to.be.true;
    });
    
    // Check images have alt text
    cy.get('img').each(($img) => {
      const img = $img[0];
      const hasAlt = img.hasAttribute('alt');
      const isDecorative = img.hasAttribute('aria-hidden') && img.getAttribute('aria-hidden') === 'true';
      
      expect(hasAlt || isDecorative).to.be.true;
    });
  });
});

/**
 * Check focus management
 */
Cypress.Commands.add('checkFocusManagement', () => {
  // Check that focus is visible
  cy.get('*:focus').should('have.css', 'outline').and('not.equal', 'none');
  
  // Check focus trap in modals
  cy.get('[role="dialog"], .modal').then($modals => {
    if ($modals.length > 0) {
      cy.wrap($modals.first()).within(() => {
        // Focus should be trapped within modal
        cy.get('button, input, select, textarea, a[href]').first().focus();
        cy.focused().tab();
        cy.focused().should('exist');
      });
    }
  });
});

/**
 * Check screen reader content
 */
Cypress.Commands.add('checkScreenReaderContent', () => {
  // Check for screen reader only content
  cy.get('.sr-only, .visually-hidden, [aria-live]').should('exist');
  
  // Check for proper heading hierarchy
  cy.get('h1, h2, h3, h4, h5, h6').then($headings => {
    const headingLevels = Array.from($headings).map(h => parseInt(h.tagName.charAt(1)));
    
    // Check that heading levels don't skip (e.g., h1 -> h3)
    for (let i = 1; i < headingLevels.length; i++) {
      const diff = headingLevels[i] - headingLevels[i - 1];
      expect(diff).to.be.at.most(1);
    }
  });
});

/**
 * Simulate screen reader interaction
 */
Cypress.Commands.add('simulateScreenReader', (selector: string) => {
  cy.get(selector).then($el => {
    const element = $el[0];
    
    // Get accessible name
    const accessibleName = element.getAttribute('aria-label') ||
                          element.getAttribute('aria-labelledby') ||
                          element.textContent;
    
    // Get role
    const role = element.getAttribute('role') || element.tagName.toLowerCase();
    
    // Log what screen reader would announce
    cy.log(`Screen reader would announce: "${accessibleName}" ${role}`);
    
    // Verify element is not hidden from screen readers
    expect(element.getAttribute('aria-hidden')).to.not.equal('true');
  });
});

/**
 * Check tab order matches visual order
 */
Cypress.Commands.add('checkTabOrder', (selectors: string[]) => {
  selectors.forEach((selector, index) => {
    cy.get(selector).focus();
    cy.focused().should('match', selector);
    
    if (index < selectors.length - 1) {
      cy.focused().tab();
    }
  });
});

/**
 * Comprehensive accessibility verification
 */
Cypress.Commands.add('verifyNoAccessibilityViolations', () => {
  cy.checkAriaLabels();
  cy.checkKeyboardNavigation();
  cy.checkFocusManagement();
  cy.checkColorContrast();
  cy.checkScreenReaderContent();
  
  cy.log('✅ All accessibility checks passed');
});
