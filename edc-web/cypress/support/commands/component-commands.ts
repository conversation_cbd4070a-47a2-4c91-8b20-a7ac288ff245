/**
 * Component testing specific custom commands for Cypress
 */

declare global {
  namespace Cypress {
    interface Chainable {
      mountComponent(component: React.ReactElement, options?: ComponentMountOptions): Chainable<any>;
      testComponentProps(component: React.ReactElement, props: any[]): Chainable<void>;
      testComponentStates(component: React.ReactElement, states: any[]): Chainable<void>;
      testComponentEvents(component: React.ReactElement, events: ComponentEvent[]): Chainable<void>;
      testComponentAccessibility(component: React.ReactElement): Chainable<void>;
      testComponentResponsive(component: React.ReactElement): Chainable<void>;
      mockComponentDependencies(mocks: ComponentMocks): Chainable<void>;
      testComponentErrorBoundary(component: React.ReactElement): Chainable<void>;
    }
  }
}

interface ComponentMountOptions {
  props?: any;
  state?: any;
  context?: any;
  providers?: boolean;
  theme?: any;
  router?: boolean;
  redux?: boolean;
}

interface ComponentEvent {
  trigger: string;
  selector: string;
  expectedResult: string;
  assertion?: string;
}

interface ComponentMocks {
  api?: any;
  localStorage?: any;
  sessionStorage?: any;
  window?: any;
}

/**
 * Enhanced component mounting with comprehensive setup
 */
Cypress.Commands.add('mountComponent', (component: React.ReactElement, options: ComponentMountOptions = {}) => {
  const {
    props = {},
    providers = true,
    theme,
    router = false,
    redux = false
  } = options;

  // Clone component with additional props
  const componentWithProps = React.cloneElement(component, props);

  if (redux) {
    return cy.mountWithRedux(componentWithProps, {
      theme,
      router,
      initialState: options.state || {}
    });
  } else if (providers) {
    return cy.mountWithProviders(componentWithProps, {
      theme,
      router
    });
  } else {
    return cy.mount(componentWithProps);
  }
});

/**
 * Test component with different prop combinations
 */
Cypress.Commands.add('testComponentProps', (component: React.ReactElement, props: any[]) => {
  props.forEach((propSet, index) => {
    cy.log(`Testing prop set ${index + 1}:`, propSet);
    
    cy.mountComponent(component, { props: propSet });
    
    // Basic render test
    cy.get('[data-cy-root]').should('exist');
    
    // Take screenshot for visual comparison
    cy.screenshot(`component-props-${index + 1}`);
  });
});

/**
 * Test component with different states
 */
Cypress.Commands.add('testComponentStates', (component: React.ReactElement, states: any[]) => {
  states.forEach((state, index) => {
    cy.log(`Testing state ${index + 1}:`, state);
    
    cy.mountComponent(component, { state });
    
    // Verify component renders in expected state
    cy.get('[data-cy-root]').should('exist');
    
    // Take screenshot for visual comparison
    cy.screenshot(`component-state-${index + 1}`);
  });
});

/**
 * Test component events and interactions
 */
Cypress.Commands.add('testComponentEvents', (component: React.ReactElement, events: ComponentEvent[]) => {
  cy.mountComponent(component);
  
  events.forEach((event, index) => {
    cy.log(`Testing event ${index + 1}: ${event.trigger} on ${event.selector}`);
    
    // Trigger the event
    cy.get(event.selector).trigger(event.trigger);
    
    // Check expected result
    if (event.assertion) {
      cy.get(event.expectedResult).should(event.assertion);
    } else {
      cy.get(event.expectedResult).should('exist');
    }
  });
});

/**
 * Test component accessibility
 */
Cypress.Commands.add('testComponentAccessibility', (component: React.ReactElement) => {
  cy.mountComponent(component);
  
  // Run accessibility checks
  cy.verifyNoAccessibilityViolations();
  
  // Test keyboard navigation
  cy.checkKeyboardNavigation('[data-cy-root]');
  
  // Test ARIA attributes
  cy.checkAriaLabels('[data-cy-root]');
  
  // Test focus management
  cy.checkFocusManagement();
  
  cy.log('✅ Component accessibility tests passed');
});

/**
 * Test component responsiveness
 */
Cypress.Commands.add('testComponentResponsive', (component: React.ReactElement) => {
  const breakpoints = [
    { name: 'mobile', width: 375, height: 667 },
    { name: 'tablet', width: 768, height: 1024 },
    { name: 'desktop', width: 1280, height: 720 },
    { name: 'large', width: 1920, height: 1080 }
  ];

  breakpoints.forEach(breakpoint => {
    cy.viewport(breakpoint.width, breakpoint.height);
    cy.mountComponent(component);
    
    // Verify component renders properly at this breakpoint
    cy.get('[data-cy-root]').should('be.visible');
    
    // Take screenshot for visual comparison
    cy.screenshot(`component-responsive-${breakpoint.name}`);
    
    cy.log(`✅ Component responsive test passed for ${breakpoint.name}`);
  });
});

/**
 * Mock component dependencies
 */
Cypress.Commands.add('mockComponentDependencies', (mocks: ComponentMocks) => {
  cy.window().then((win) => {
    // Mock API calls
    if (mocks.api) {
      Object.keys(mocks.api).forEach(endpoint => {
        cy.intercept('GET', endpoint, mocks.api[endpoint]).as(`mock-${endpoint.replace(/\//g, '-')}`);
      });
    }

    // Mock localStorage
    if (mocks.localStorage) {
      Object.keys(mocks.localStorage).forEach(key => {
        win.localStorage.setItem(key, JSON.stringify(mocks.localStorage[key]));
      });
    }

    // Mock sessionStorage
    if (mocks.sessionStorage) {
      Object.keys(mocks.sessionStorage).forEach(key => {
        win.sessionStorage.setItem(key, JSON.stringify(mocks.sessionStorage[key]));
      });
    }

    // Mock window properties
    if (mocks.window) {
      Object.keys(mocks.window).forEach(prop => {
        win[prop] = mocks.window[prop];
      });
    }
  });
});

/**
 * Test component error boundary
 */
Cypress.Commands.add('testComponentErrorBoundary', (component: React.ReactElement) => {
  // Create a component that will throw an error
  const ErrorComponent = () => {
    throw new Error('Test error for error boundary');
  };

  // Mount with error boundary
  const ComponentWithErrorBoundary = () => (
    <ErrorBoundary>
      <ErrorComponent />
    </ErrorBoundary>
  );

  cy.mountComponent(<ComponentWithErrorBoundary />);
  
  // Verify error boundary catches the error
  cy.get('[data-testid="error-boundary"]').should('exist');
  cy.get('[data-testid="error-message"]').should('contain', 'Something went wrong');
  
  cy.log('✅ Error boundary test passed');
});

// Error Boundary component for testing
const ErrorBoundary = ({ children }) => {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const errorHandler = (error) => {
      setHasError(true);
    };

    window.addEventListener('error', errorHandler);
    return () => window.removeEventListener('error', errorHandler);
  }, []);

  if (hasError) {
    return (
      <div data-testid="error-boundary">
        <h2 data-testid="error-message">Something went wrong</h2>
      </div>
    );
  }

  return children;
};
