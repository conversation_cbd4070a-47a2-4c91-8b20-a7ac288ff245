// Import commands
import './commands/auth-commands';
import './commands/api-commands';
import './commands/ui-commands';
import './commands/accessibility-commands';
import './commands/data-commands';

// Import utilities
import './utils/test-helpers';

// Terminal report for better logging
require('cypress-terminal-report/src/installLogsCollector')();

// Global error handling
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignore specific errors that don't affect test functionality
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  if (err.message.includes('Script error')) {
    return false;
  }

  // Log the error for debugging
  console.error('Uncaught exception:', err);
  return false;
});

// Global before hook for test setup
beforeEach(() => {
  // Clear local storage and session storage
  cy.clearLocalStorage();
  cy.clearCookies();

  // Set up common intercepts
  cy.setupCommonIntercepts();

  // Set viewport for consistent testing
  cy.viewport(1280, 720);
});

// Global after hook for cleanup
afterEach(() => {
  // Clean up any test data if needed
  cy.task('clearTestData', null, { failOnStatusCode: false });
});

// Add custom assertions
chai.use((chai, utils) => {
  chai.Assertion.addMethod('beAccessible', function () {
    const obj = this._obj;

    new chai.Assertion(obj).to.exist;

    // Check for basic accessibility attributes
    cy.wrap(obj).should('have.attr', 'role').or('have.attr', 'aria-label').or('have.attr', 'aria-labelledby');
  });
});
