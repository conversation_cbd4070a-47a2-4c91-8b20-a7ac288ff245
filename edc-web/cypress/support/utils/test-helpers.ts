/**
 * Test helper utilities for Cypress tests
 */

/**
 * Generate random test data
 */
export const TestDataGenerator = {
  /**
   * Generate random string
   */
  randomString(length = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate random email
   */
  randomEmail(): string {
    return `test.${this.randomString(8)}@example.com`;
  },

  /**
   * Generate random number within range
   */
  randomNumber(min = 1, max = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Generate random date
   */
  randomDate(start = new Date(2020, 0, 1), end = new Date()): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  },

  /**
   * Generate random user data
   */
  randomUser(): any {
    return {
      id: this.randomNumber(1, 10000),
      email: this.randomEmail(),
      firstName: this.randomString(6),
      lastName: this.randomString(8),
      role: ['user', 'admin', 'moderator'][this.randomNumber(0, 2)],
      isActive: Math.random() > 0.5,
      createdAt: this.randomDate().toISOString()
    };
  },

  /**
   * Generate random content data
   */
  randomContent(): any {
    return {
      id: this.randomNumber(1, 10000),
      title: `Test Content ${this.randomString(10)}`,
      description: `This is a test description ${this.randomString(20)}`,
      type: ['article', 'video', 'course', 'document'][this.randomNumber(0, 3)],
      status: ['draft', 'published', 'archived'][this.randomNumber(0, 2)],
      tags: [this.randomString(5), this.randomString(6), this.randomString(7)],
      createdAt: this.randomDate().toISOString()
    };
  }
};

/**
 * Wait utilities
 */
export const WaitHelpers = {
  /**
   * Wait for element to be stable (not moving/changing)
   */
  waitForStable(selector: string, timeout = 5000): void {
    let lastPosition: any = null;
    let stableCount = 0;
    const requiredStableChecks = 3;

    const checkStability = () => {
      cy.get(selector).then($el => {
        const currentPosition = $el.offset();
        
        if (lastPosition && 
            currentPosition.top === lastPosition.top && 
            currentPosition.left === lastPosition.left) {
          stableCount++;
        } else {
          stableCount = 0;
        }
        
        lastPosition = currentPosition;
        
        if (stableCount < requiredStableChecks) {
          cy.wait(100).then(checkStability);
        }
      });
    };

    checkStability();
  },

  /**
   * Wait for network to be idle
   */
  waitForNetworkIdle(timeout = 5000): void {
    cy.window().then(win => {
      let requestCount = 0;
      const originalFetch = win.fetch;
      
      win.fetch = (...args) => {
        requestCount++;
        return originalFetch(...args).finally(() => {
          requestCount--;
        });
      };

      const checkIdle = () => {
        if (requestCount === 0) {
          return;
        }
        cy.wait(100).then(checkIdle);
      };

      checkIdle();
    });
  }
};

/**
 * DOM utilities
 */
export const DOMHelpers = {
  /**
   * Check if element is in viewport
   */
  isInViewport(selector: string): Cypress.Chainable<boolean> {
    return cy.get(selector).then($el => {
      const element = $el[0];
      const rect = element.getBoundingClientRect();
      
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= Cypress.config('viewportHeight') &&
        rect.right <= Cypress.config('viewportWidth')
      );
    });
  },

  /**
   * Get element's computed style property
   */
  getComputedStyle(selector: string, property: string): Cypress.Chainable<string> {
    return cy.get(selector).then($el => {
      const element = $el[0];
      return window.getComputedStyle(element).getPropertyValue(property);
    });
  },

  /**
   * Check if element has specific class
   */
  hasClass(selector: string, className: string): Cypress.Chainable<boolean> {
    return cy.get(selector).then($el => {
      return $el.hasClass(className);
    });
  }
};

/**
 * Form utilities
 */
export const FormHelpers = {
  /**
   * Fill form with data object
   */
  fillForm(formData: Record<string, any>, formSelector = 'form'): void {
    cy.get(formSelector).within(() => {
      Object.keys(formData).forEach(fieldName => {
        const value = formData[fieldName];
        
        // Handle different input types
        cy.get(`[name="${fieldName}"], #${fieldName}, [data-testid="${fieldName}"]`).then($field => {
          const fieldType = $field.attr('type') || $field.prop('tagName').toLowerCase();
          
          switch (fieldType) {
            case 'checkbox':
              if (value) {
                cy.wrap($field).check();
              } else {
                cy.wrap($field).uncheck();
              }
              break;
            case 'radio':
              cy.wrap($field).check();
              break;
            case 'select':
              cy.wrap($field).select(value);
              break;
            default:
              cy.wrap($field).clear().type(value);
          }
        });
      });
    });
  },

  /**
   * Validate form errors
   */
  validateFormErrors(expectedErrors: Record<string, string>): void {
    Object.keys(expectedErrors).forEach(fieldName => {
      const errorMessage = expectedErrors[fieldName];
      cy.get(`[data-testid="${fieldName}-error"], .error-${fieldName}, .field-error`)
        .should('contain', errorMessage);
    });
  }
};

/**
 * API testing utilities
 */
export const APIHelpers = {
  /**
   * Validate API response structure
   */
  validateApiResponse(alias: string, expectedStructure: any): void {
    cy.wait(`@${alias}`).then((interception) => {
      const response = interception.response.body;
      
      Object.keys(expectedStructure).forEach(key => {
        expect(response).to.have.property(key);
        
        if (typeof expectedStructure[key] === 'object' && expectedStructure[key] !== null) {
          expect(response[key]).to.be.an('object');
        } else {
          expect(typeof response[key]).to.equal(typeof expectedStructure[key]);
        }
      });
    });
  },

  /**
   * Mock API with delay
   */
  mockApiWithDelay(method: string, url: string, response: any, delay = 1000): void {
    cy.intercept(method, url, (req) => {
      req.reply((res) => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(res.send(response));
          }, delay);
        });
      });
    });
  }
};

/**
 * Performance testing utilities
 */
export const PerformanceHelpers = {
  /**
   * Measure page load time
   */
  measurePageLoad(): Cypress.Chainable<number> {
    return cy.window().then(win => {
      return win.performance.timing.loadEventEnd - win.performance.timing.navigationStart;
    });
  },

  /**
   * Check for memory leaks
   */
  checkMemoryUsage(): Cypress.Chainable<any> {
    return cy.window().then(win => {
      if ('memory' in win.performance) {
        return {
          used: win.performance.memory.usedJSHeapSize,
          total: win.performance.memory.totalJSHeapSize,
          limit: win.performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
  }
};

/**
 * Screenshot and visual testing utilities
 */
export const VisualHelpers = {
  /**
   * Take screenshot with timestamp
   */
  screenshotWithTimestamp(name: string): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    cy.screenshot(`${name}-${timestamp}`);
  },

  /**
   * Compare visual elements
   */
  compareVisual(selector: string, baseline: string): void {
    cy.get(selector).screenshot(baseline, { overwrite: true });
    // Note: Visual comparison would require additional plugins like cypress-image-diff
  }
};
