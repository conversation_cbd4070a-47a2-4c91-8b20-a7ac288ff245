/**
 * Mock Redux store utilities for component testing
 */

import { createStore, combineReducers, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';

// Mock reducers that match the application structure
const mockReducers = {
  // User state
  currentUser: (state = {
    id: null,
    email: '',
    name: '',
    role: 'user',
    isLoggedIn: false,
    preferences: {}
  }, action) => {
    switch (action.type) {
      case 'SET_CURRENT_USER':
        return { ...state, ...action.payload };
      case 'LOGIN_SUCCESS':
        return { ...state, isLoggedIn: true, ...action.payload };
      case 'LOGOUT':
        return { ...state, isLoggedIn: false, id: null };
      default:
        return state;
    }
  },

  // Team/Organization state
  team: (state = {
    id: null,
    name: '',
    settings: {},
    pending: false
  }, action) => {
    switch (action.type) {
      case 'SET_TEAM':
        return { ...state, ...action.payload };
      case 'SET_TEAM_PENDING':
        return { ...state, pending: action.payload };
      default:
        return state;
    }
  },

  // Theme state
  theme: (state = {
    themeId: 'default',
    groupThemeName: 'default',
    accessibility: false
  }, action) => {
    switch (action.type) {
      case 'SET_THEME':
        return { ...state, ...action.payload };
      default:
        return state;
    }
  },

  // Modal state
  modal: (state = {
    isOpen: false,
    type: null,
    data: null
  }, action) => {
    switch (action.type) {
      case 'OPEN_MODAL':
        return { isOpen: true, type: action.modalType, data: action.data };
      case 'CLOSE_MODAL':
        return { isOpen: false, type: null, data: null };
      default:
        return state;
    }
  },

  // Snackbar state
  snackbar: (state = {
    isOpen: false,
    message: '',
    type: 'info'
  }, action) => {
    switch (action.type) {
      case 'SHOW_SNACKBAR':
        return { isOpen: true, message: action.message, type: action.snackbarType };
      case 'HIDE_SNACKBAR':
        return { isOpen: false, message: '', type: 'info' };
      default:
        return state;
    }
  },

  // Content/Discovery state
  discovery: (state = {
    items: [],
    loading: false,
    error: null,
    filters: {},
    pagination: { page: 1, totalPages: 1 }
  }, action) => {
    switch (action.type) {
      case 'FETCH_CONTENT_START':
        return { ...state, loading: true, error: null };
      case 'FETCH_CONTENT_SUCCESS':
        return { ...state, loading: false, items: action.payload };
      case 'FETCH_CONTENT_ERROR':
        return { ...state, loading: false, error: action.error };
      default:
        return state;
    }
  },

  // Search state
  search: (state = {
    query: '',
    results: [],
    loading: false,
    filters: {},
    suggestions: []
  }, action) => {
    switch (action.type) {
      case 'SET_SEARCH_QUERY':
        return { ...state, query: action.query };
      case 'SEARCH_START':
        return { ...state, loading: true };
      case 'SEARCH_SUCCESS':
        return { ...state, loading: false, results: action.results };
      default:
        return state;
    }
  },

  // Notifications state
  notifications: (state = {
    items: [],
    unreadCount: 0,
    loading: false
  }, action) => {
    switch (action.type) {
      case 'FETCH_NOTIFICATIONS_SUCCESS':
        return { ...state, items: action.payload, unreadCount: action.unreadCount };
      case 'MARK_NOTIFICATION_READ':
        return {
          ...state,
          items: state.items.map(item =>
            item.id === action.id ? { ...item, read: true } : item
          ),
          unreadCount: Math.max(0, state.unreadCount - 1)
        };
      default:
        return state;
    }
  }
};

/**
 * Create a mock Redux store with default or custom initial state
 */
export function createMockStore(initialState = {}) {
  const defaultState = {
    currentUser: {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      isLoggedIn: true,
      preferences: {}
    },
    team: {
      id: 1,
      name: 'Test Organization',
      settings: {},
      pending: false
    },
    theme: {
      themeId: 'default',
      groupThemeName: 'default',
      accessibility: false
    },
    modal: {
      isOpen: false,
      type: null,
      data: null
    },
    snackbar: {
      isOpen: false,
      message: '',
      type: 'info'
    },
    discovery: {
      items: [],
      loading: false,
      error: null,
      filters: {},
      pagination: { page: 1, totalPages: 1 }
    },
    search: {
      query: '',
      results: [],
      loading: false,
      filters: {},
      suggestions: []
    },
    notifications: {
      items: [],
      unreadCount: 0,
      loading: false
    }
  };

  const mergedState = { ...defaultState, ...initialState };
  const rootReducer = combineReducers(mockReducers);
  
  return createStore(rootReducer, mergedState, applyMiddleware(thunk));
}

/**
 * Create mock store with authenticated user
 */
export function createAuthenticatedMockStore(userOverrides = {}) {
  return createMockStore({
    currentUser: {
      id: 1,
      email: '<EMAIL>',
      name: 'Authenticated User',
      role: 'user',
      isLoggedIn: true,
      preferences: {},
      ...userOverrides
    }
  });
}

/**
 * Create mock store with admin user
 */
export function createAdminMockStore(userOverrides = {}) {
  return createMockStore({
    currentUser: {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      isLoggedIn: true,
      preferences: {},
      ...userOverrides
    }
  });
}

/**
 * Create mock store with loading states
 */
export function createLoadingMockStore() {
  return createMockStore({
    discovery: {
      items: [],
      loading: true,
      error: null,
      filters: {},
      pagination: { page: 1, totalPages: 1 }
    },
    search: {
      query: 'test',
      results: [],
      loading: true,
      filters: {},
      suggestions: []
    }
  });
}

/**
 * Create mock store with error states
 */
export function createErrorMockStore() {
  return createMockStore({
    discovery: {
      items: [],
      loading: false,
      error: 'Failed to load content',
      filters: {},
      pagination: { page: 1, totalPages: 1 }
    }
  });
}
