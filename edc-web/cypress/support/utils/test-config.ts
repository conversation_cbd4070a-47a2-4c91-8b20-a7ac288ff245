/**
 * Test configuration utilities for different environments and scenarios
 */

export interface TestConfig {
  baseUrl: string;
  apiUrl: string;
  timeout: number;
  retries: number;
  viewport: {
    width: number;
    height: number;
  };
  credentials: {
    admin: {
      email: string;
      password: string;
    };
    user: {
      email: string;
      password: string;
    };
  };
  features: {
    [key: string]: boolean;
  };
}

/**
 * Default test configuration
 */
export const defaultConfig: TestConfig = {
  baseUrl: 'http://local.lvh.me:4001',
  apiUrl: 'http://localhost:8001/api',
  timeout: 10000,
  retries: 2,
  viewport: {
    width: 1280,
    height: 720
  },
  credentials: {
    admin: {
      email: '<EMAIL>',
      password: 'Rajbits@149'
    },
    user: {
      email: '<EMAIL>',
      password: 'Rajbits@149'
    }
  },
  features: {
    search: true,
    socialLearning: true,
    analytics: true,
    contentCreation: true,
    messaging: true
  }
};

/**
 * Environment-specific configurations
 */
export const environmentConfigs: Record<string, Partial<TestConfig>> = {
  development: {
    baseUrl: 'http://local.lvh.me:4001',
    apiUrl: 'http://localhost:8001/api',
    timeout: 15000,
    retries: 1
  },
  
  staging: {
    baseUrl: 'https://staging.edcast.com',
    apiUrl: 'https://staging.edcast.com/api',
    timeout: 20000,
    retries: 3,
    credentials: {
      admin: {
        email: '<EMAIL>',
        password: 'staging-admin-password'
      },
      user: {
        email: '<EMAIL>',
        password: 'staging-user-password'
      }
    }
  },
  
  production: {
    baseUrl: 'https://app.edcast.com',
    apiUrl: 'https://app.edcast.com/api',
    timeout: 30000,
    retries: 5,
    credentials: {
      admin: {
        email: '<EMAIL>',
        password: 'production-admin-password'
      },
      user: {
        email: '<EMAIL>',
        password: 'production-user-password'
      }
    }
  }
};

/**
 * Device-specific viewport configurations
 */
export const deviceConfigs = {
  mobile: {
    viewport: { width: 375, height: 667 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  },
  
  tablet: {
    viewport: { width: 768, height: 1024 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  },
  
  desktop: {
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
  },
  
  largeDesktop: {
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
  }
};

/**
 * Test suite configurations
 */
export const testSuiteConfigs = {
  smoke: {
    timeout: 5000,
    retries: 0,
    features: {
      search: true,
      login: true,
      navigation: true
    }
  },
  
  regression: {
    timeout: 15000,
    retries: 2,
    features: {
      search: true,
      contentCreation: true,
      socialLearning: true,
      analytics: true,
      messaging: true,
      profile: true
    }
  },
  
  accessibility: {
    timeout: 20000,
    retries: 1,
    features: {
      keyboardNavigation: true,
      screenReader: true,
      colorContrast: true,
      focusManagement: true
    }
  },
  
  performance: {
    timeout: 30000,
    retries: 3,
    features: {
      pageLoad: true,
      apiResponse: true,
      rendering: true,
      memory: true
    }
  }
};

/**
 * Get configuration for current environment
 */
export function getTestConfig(): TestConfig {
  const environment = Cypress.env('environment') || 'development';
  const envConfig = environmentConfigs[environment] || {};
  
  return {
    ...defaultConfig,
    ...envConfig
  };
}

/**
 * Get device configuration
 */
export function getDeviceConfig(device: string) {
  return deviceConfigs[device] || deviceConfigs.desktop;
}

/**
 * Get test suite configuration
 */
export function getTestSuiteConfig(suite: string) {
  return testSuiteConfigs[suite] || {};
}

/**
 * Setup test environment based on configuration
 */
export function setupTestEnvironment(options: {
  environment?: string;
  device?: string;
  suite?: string;
} = {}) {
  const config = getTestConfig();
  const deviceConfig = options.device ? getDeviceConfig(options.device) : null;
  const suiteConfig = options.suite ? getTestSuiteConfig(options.suite) : {};
  
  // Set viewport if device specified
  if (deviceConfig) {
    cy.viewport(deviceConfig.viewport.width, deviceConfig.viewport.height);
  } else {
    cy.viewport(config.viewport.width, config.viewport.height);
  }
  
  // Set timeouts based on suite configuration
  if (suiteConfig.timeout) {
    Cypress.config('defaultCommandTimeout', suiteConfig.timeout);
  }
  
  // Set retries based on suite configuration
  if (suiteConfig.retries !== undefined) {
    Cypress.config('retries', suiteConfig.retries);
  }
  
  return config;
}

/**
 * Feature flag utilities
 */
export class FeatureFlags {
  private config: TestConfig;
  
  constructor(config?: TestConfig) {
    this.config = config || getTestConfig();
  }
  
  isEnabled(feature: string): boolean {
    return this.config.features[feature] === true;
  }
  
  skipIfDisabled(feature: string, testFn: () => void) {
    if (this.isEnabled(feature)) {
      testFn();
    } else {
      it.skip(`${feature} is disabled in current configuration`, () => {});
    }
  }
  
  runOnlyIfEnabled(feature: string, testFn: () => void) {
    if (this.isEnabled(feature)) {
      testFn();
    }
  }
}

/**
 * Test data configuration
 */
export const testDataConfig = {
  users: {
    admin: {
      count: 1,
      roles: ['admin', 'super_admin']
    },
    regular: {
      count: 5,
      roles: ['user']
    },
    moderator: {
      count: 2,
      roles: ['moderator']
    }
  },
  
  content: {
    courses: {
      count: 10,
      types: ['video', 'article', 'interactive']
    },
    articles: {
      count: 20,
      categories: ['technology', 'business', 'design']
    }
  },
  
  groups: {
    public: {
      count: 3,
      memberCount: 50
    },
    private: {
      count: 2,
      memberCount: 20
    }
  }
};

/**
 * API endpoint configuration
 */
export const apiEndpoints = {
  auth: {
    login: '/auth/sign_in.json',
    logout: '/auth/sign_out.json',
    refresh: '/auth/refresh.json',
    validate: '/api/users/validate_session.json'
  },
  
  users: {
    info: '/api/users/info.json',
    profile: '/api/users/:id/profile',
    preferences: '/api/users/:id/preferences'
  },
  
  content: {
    list: '/api/content',
    details: '/api/content/:id/details',
    create: '/api/content',
    update: '/api/content/:id',
    delete: '/api/content/:id'
  },
  
  search: {
    query: '/api/search',
    suggestions: '/api/search/suggestions',
    filters: '/api/search/filters'
  }
};

/**
 * Test timing configuration
 */
export const timingConfig = {
  fast: 1000,      // Quick operations
  normal: 5000,    // Standard operations
  slow: 15000,     // Complex operations
  network: 30000,  // Network-dependent operations
  
  // Animation timings
  fadeIn: 300,
  slideIn: 500,
  modalOpen: 400,
  
  // Debounce timings
  searchDebounce: 300,
  inputDebounce: 500,
  
  // Polling intervals
  statusCheck: 2000,
  progressUpdate: 1000
};
