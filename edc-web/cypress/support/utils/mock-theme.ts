/**
 * Mock theme utilities for component testing
 */

// Default theme object that matches the application's theme structure
export const mockTheme = {
  // Colors
  colors: {
    primary: '#007bff',
    secondary: '#6c757d',
    success: '#28a745',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40',
    white: '#ffffff',
    black: '#000000',
    
    // Text colors
    text: {
      primary: '#212529',
      secondary: '#6c757d',
      muted: '#868e96',
      white: '#ffffff'
    },
    
    // Background colors
    background: {
      primary: '#ffffff',
      secondary: '#f8f9fa',
      dark: '#343a40',
      light: '#f8f9fa'
    },
    
    // Border colors
    border: {
      primary: '#dee2e6',
      secondary: '#e9ecef',
      light: '#f8f9fa'
    }
  },

  // Typography
  typography: {
    fontFamily: {
      primary: '"Helvetica Neue", Helvetica, Arial, sans-serif',
      secondary: 'Georgia, "Times New Roman", Times, serif',
      monospace: '"SFMono-Regular", Con<PERSON><PERSON>, "Liberation Mono", Menlo, Courier, monospace'
    },
    
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem'
    },
    
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800
    },
    
    lineHeight: {
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    }
  },

  // Spacing
  spacing: {
    0: '0',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    32: '8rem'
  },

  // Breakpoints
  breakpoints: {
    xs: '0px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    xxl: '1400px'
  },

  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none'
  },

  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    base: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px'
  },

  // Z-index
  zIndex: {
    auto: 'auto',
    0: 0,
    10: 10,
    20: 20,
    30: 30,
    40: 40,
    50: 50,
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modalBackdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070
  },

  // Transitions
  transitions: {
    fast: '150ms ease-in-out',
    base: '250ms ease-in-out',
    slow: '350ms ease-in-out'
  },

  // Component-specific styles
  components: {
    button: {
      padding: {
        sm: '0.25rem 0.5rem',
        base: '0.5rem 1rem',
        lg: '0.75rem 1.5rem'
      },
      borderRadius: '0.25rem',
      fontWeight: 500
    },
    
    input: {
      padding: '0.5rem 0.75rem',
      borderRadius: '0.25rem',
      borderWidth: '1px',
      fontSize: '1rem'
    },
    
    card: {
      padding: '1.5rem',
      borderRadius: '0.5rem',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
    },
    
    modal: {
      borderRadius: '0.5rem',
      padding: '1.5rem',
      maxWidth: '32rem'
    }
  }
};

/**
 * Create a dark theme variant
 */
export const mockDarkTheme = {
  ...mockTheme,
  colors: {
    ...mockTheme.colors,
    primary: '#0d6efd',
    text: {
      primary: '#ffffff',
      secondary: '#adb5bd',
      muted: '#6c757d',
      white: '#000000'
    },
    background: {
      primary: '#212529',
      secondary: '#343a40',
      dark: '#ffffff',
      light: '#495057'
    },
    border: {
      primary: '#495057',
      secondary: '#343a40',
      light: '#212529'
    }
  }
};

/**
 * Create a high contrast theme for accessibility testing
 */
export const mockHighContrastTheme = {
  ...mockTheme,
  colors: {
    ...mockTheme.colors,
    primary: '#000000',
    secondary: '#ffffff',
    text: {
      primary: '#000000',
      secondary: '#000000',
      muted: '#000000',
      white: '#ffffff'
    },
    background: {
      primary: '#ffffff',
      secondary: '#ffffff',
      dark: '#000000',
      light: '#ffffff'
    },
    border: {
      primary: '#000000',
      secondary: '#000000',
      light: '#000000'
    }
  }
};

/**
 * Create a custom theme with overrides
 */
export function createCustomTheme(overrides = {}) {
  return {
    ...mockTheme,
    ...overrides,
    colors: {
      ...mockTheme.colors,
      ...(overrides.colors || {})
    },
    typography: {
      ...mockTheme.typography,
      ...(overrides.typography || {})
    },
    spacing: {
      ...mockTheme.spacing,
      ...(overrides.spacing || {})
    }
  };
}

/**
 * Theme variants for testing different scenarios
 */
export const themeVariants = {
  default: mockTheme,
  dark: mockDarkTheme,
  highContrast: mockHighContrastTheme,
  
  // Brand-specific themes
  corporate: createCustomTheme({
    colors: {
      primary: '#003366',
      secondary: '#0066cc'
    }
  }),
  
  educational: createCustomTheme({
    colors: {
      primary: '#2e7d32',
      secondary: '#4caf50'
    }
  }),
  
  healthcare: createCustomTheme({
    colors: {
      primary: '#1976d2',
      secondary: '#42a5f5'
    }
  })
};
