import { mount } from 'cypress/react18';
import React from 'react';
import { Provider } from 'react-redux';
import { B<PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { Translatr } from 'centralized-design-system/src/Translatr';

// Import test utilities
import { createMockStore } from './utils/mock-store';
import { mockTheme } from './utils/mock-theme';

// Import custom commands
import './commands/component-commands';
import './commands/accessibility-commands';

declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
      mountWithProviders: (
        component: React.ReactElement,
        options?: MountOptions
      ) => Cypress.Chainable<any>;
      mountWithRedux: (
        component: React.ReactElement,
        options?: MountWithReduxOptions
      ) => Cypress.Chainable<any>;
    }
  }
}

interface MountOptions {
  theme?: any;
  router?: boolean;
  translations?: string[];
}

interface MountWithReduxOptions extends MountOptions {
  initialState?: any;
  store?: any;
}

// Enhanced mount command with providers
Cypress.Commands.add('mountWithProviders', (component: React.ReactElement, options: MountOptions = {}) => {
  const {
    theme = mockTheme,
    router = true,
    translations = ['cds.common.main', 'web.common.main']
  } = options;

  let wrappedComponent = component;

  // Wrap with ThemeProvider
  wrappedComponent = (
    <ThemeProvider theme={theme}>
      {wrappedComponent}
    </ThemeProvider>
  );

  // Wrap with Router if needed
  if (router) {
    wrappedComponent = (
      <BrowserRouter>
        {wrappedComponent}
      </BrowserRouter>
    );
  }

  // Wrap with Translatr for translations
  wrappedComponent = (
    <Translatr apps={translations}>
      {wrappedComponent}
    </Translatr>
  );

  return cy.mount(wrappedComponent);
});

// Mount command with Redux store
Cypress.Commands.add('mountWithRedux', (component: React.ReactElement, options: MountWithReduxOptions = {}) => {
  const {
    initialState = {},
    store = createMockStore(initialState),
    theme = mockTheme,
    router = true,
    translations = ['cds.common.main', 'web.common.main']
  } = options;

  let wrappedComponent = (
    <Provider store={store}>
      {component}
    </Provider>
  );

  // Wrap with ThemeProvider
  wrappedComponent = (
    <ThemeProvider theme={theme}>
      {wrappedComponent}
    </ThemeProvider>
  );

  // Wrap with Router if needed
  if (router) {
    wrappedComponent = (
      <BrowserRouter>
        {wrappedComponent}
      </BrowserRouter>
    );
  }

  // Wrap with Translatr for translations
  wrappedComponent = (
    <Translatr apps={translations}>
      {wrappedComponent}
    </Translatr>
  );

  return cy.mount(wrappedComponent);
});

// Basic mount command
Cypress.Commands.add('mount', mount);

// Global error handling for component tests
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignore specific React/component errors that don't affect test functionality
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Warning: ReactDOM.render is no longer supported')) {
    return false;
  }

  console.error('Component test uncaught exception:', err);
  return false;
});
