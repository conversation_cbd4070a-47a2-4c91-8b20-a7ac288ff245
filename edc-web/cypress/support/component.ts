import { mount } from 'cypress/react18';
import React from 'react';

declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
      mountComponent: (component: React.ReactElement) => Cypress.Chainable<any>;
    }
  }
}

// Basic mount command
Cypress.Commands.add('mount', mount);

// Simple component mount command
Cypress.Commands.add('mountComponent', (component: React.ReactElement) => {
  return cy.mount(component);
});

// Global error handling for component tests
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignore specific React/component errors that don't affect test functionality
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Warning: ReactDOM.render is no longer supported')) {
    return false;
  }

  console.error('Component test uncaught exception:', err);
  return false;
});
