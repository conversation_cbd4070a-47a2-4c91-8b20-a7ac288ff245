/**
 * E2E tests for search functionality
 * 
 * This demonstrates comprehensive E2E testing including:
 * - User authentication
 * - Search workflows
 * - API integration
 * - Error handling
 * - Performance testing
 */

describe('Search Functionality', () => {
  beforeEach(() => {
    // Setup test environment
    cy.setupTestEnvironment();
    
    // Setup API intercepts
    cy.interceptSearchApi();
    
    // Login as authenticated user
    cy.loginAsUser();
    
    // Navigate to search page
    cy.visit('/search');
    cy.waitForPageLoad();
  });

  afterEach(() => {
    // Clean up test data
    cy.resetTestEnvironment();
  });

  describe('Basic Search', () => {
    it('should perform a basic search', () => {
      const searchQuery = 'javascript';
      
      // Enter search query
      cy.getByTestId('search-input')
        .should('be.visible')
        .type(searchQuery);
      
      // Submit search
      cy.getByTestId('search-button').click();
      
      // Wait for search results
      cy.waitForApiCall('searchResults');
      
      // Verify results are displayed
      cy.getByTestId('search-results')
        .should('be.visible')
        .should('contain', 'javascript');
      
      // Verify URL contains search query
      cy.url().should('include', `q=${searchQuery}`);
      
      // Verify search analytics
      cy.window().then((win) => {
        expect(win.analytics).to.have.been.calledWith('search_performed', {
          query: searchQuery
        });
      });
    });

    it('should handle empty search gracefully', () => {
      // Try to search with empty query
      cy.getByTestId('search-button').click();
      
      // Should show validation message
      cy.getByTestId('search-error')
        .should('be.visible')
        .should('contain', 'Please enter a search term');
      
      // Should not make API call
      cy.get('@searchResults.all').should('have.length', 0);
    });

    it('should show search suggestions', () => {
      // Start typing
      cy.getByTestId('search-input').type('java');
      
      // Wait for suggestions
      cy.waitForApiCall('searchSuggestions');
      
      // Verify suggestions appear
      cy.getByTestId('search-suggestions')
        .should('be.visible')
        .within(() => {
          cy.get('[data-testid^="suggestion-"]')
            .should('have.length.greaterThan', 0)
            .first()
            .should('contain', 'java');
        });
    });

    it('should select suggestion and perform search', () => {
      // Type partial query
      cy.getByTestId('search-input').type('java');
      
      // Wait for suggestions
      cy.waitForApiCall('searchSuggestions');
      
      // Click on first suggestion
      cy.getByTestId('suggestion-0').click();
      
      // Verify input is updated
      cy.getByTestId('search-input').should('have.value', 'javascript');
      
      // Verify search is performed
      cy.waitForApiCall('searchResults');
      cy.getByTestId('search-results').should('be.visible');
    });
  });

  describe('Advanced Search', () => {
    it('should use search filters', () => {
      const searchQuery = 'programming';
      
      // Perform initial search
      cy.getByTestId('search-input').type(searchQuery);
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchResults');
      
      // Apply content type filter
      cy.getByTestId('filter-content-type').click();
      cy.getByTestId('filter-option-course').click();
      
      // Apply difficulty filter
      cy.getByTestId('filter-difficulty').click();
      cy.getByTestId('filter-option-beginner').click();
      
      // Apply filters
      cy.getByTestId('apply-filters-button').click();
      
      // Wait for filtered results
      cy.waitForApiCall('searchResults');
      
      // Verify filters are applied in URL
      cy.url()
        .should('include', 'type=course')
        .should('include', 'difficulty=beginner');
      
      // Verify filtered results
      cy.getByTestId('search-results').within(() => {
        cy.get('[data-testid^="result-"]').each(($result) => {
          cy.wrap($result)
            .should('contain', 'Course')
            .should('contain', 'Beginner');
        });
      });
    });

    it('should clear filters', () => {
      // Apply some filters first
      cy.getByTestId('search-input').type('test');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchResults');
      
      cy.getByTestId('filter-content-type').click();
      cy.getByTestId('filter-option-course').click();
      cy.getByTestId('apply-filters-button').click();
      cy.waitForApiCall('searchResults');
      
      // Clear filters
      cy.getByTestId('clear-filters-button').click();
      
      // Verify filters are cleared
      cy.url().should('not.include', 'type=course');
      cy.getByTestId('filter-option-course').should('not.be.checked');
      
      // Verify results are updated
      cy.waitForApiCall('searchResults');
    });
  });

  describe('Search Results', () => {
    beforeEach(() => {
      // Perform a search to get results
      cy.getByTestId('search-input').type('javascript');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchResults');
    });

    it('should display search results correctly', () => {
      cy.getByTestId('search-results').within(() => {
        // Check result count
        cy.getByTestId('results-count')
          .should('be.visible')
          .should('contain', 'results found');
        
        // Check individual results
        cy.get('[data-testid^="result-"]').should('have.length.greaterThan', 0);
        
        // Check first result structure
        cy.getByTestId('result-0').within(() => {
          cy.getByTestId('result-title').should('be.visible');
          cy.getByTestId('result-description').should('be.visible');
          cy.getByTestId('result-author').should('be.visible');
          cy.getByTestId('result-rating').should('be.visible');
        });
      });
    });

    it('should navigate to content when clicked', () => {
      // Click on first result
      cy.getByTestId('result-0').click();
      
      // Should navigate to content page
      cy.url().should('include', '/content/');
      
      // Verify content page loads
      cy.getByTestId('content-title').should('be.visible');
    });

    it('should handle pagination', () => {
      // Scroll to pagination
      cy.getByTestId('pagination').scrollIntoView();
      
      // Click next page
      cy.getByTestId('pagination-next').click();
      
      // Wait for new results
      cy.waitForApiCall('searchResults');
      
      // Verify URL updated
      cy.url().should('include', 'page=2');
      
      // Verify new results loaded
      cy.getByTestId('search-results').should('be.visible');
      
      // Go back to first page
      cy.getByTestId('pagination-prev').click();
      cy.waitForApiCall('searchResults');
      cy.url().should('include', 'page=1');
    });
  });

  describe('Error Handling', () => {
    it('should handle search API errors gracefully', () => {
      // Mock API error
      cy.mockApiError('GET', '/api/search*', 500, 'searchError');
      
      // Perform search
      cy.getByTestId('search-input').type('test');
      cy.getByTestId('search-button').click();
      
      // Wait for error response
      cy.waitForApiCall('searchError');
      
      // Verify error message is displayed
      cy.getByTestId('search-error')
        .should('be.visible')
        .should('contain', 'Unable to perform search');
      
      // Verify retry button is available
      cy.getByTestId('retry-search-button').should('be.visible');
    });

    it('should retry failed searches', () => {
      // Mock initial error then success
      cy.mockApiError('GET', '/api/search*', 500, 'searchError');
      
      cy.getByTestId('search-input').type('test');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchError');
      
      // Setup successful response for retry
      cy.interceptSearchApi();
      
      // Click retry
      cy.getByTestId('retry-search-button').click();
      
      // Verify successful results
      cy.waitForApiCall('searchResults');
      cy.getByTestId('search-results').should('be.visible');
    });

    it('should handle no results found', () => {
      // Mock empty results
      cy.mockApiResponse('GET', '/api/search*', {
        results: [],
        pagination: { totalResults: 0 }
      }, 'emptyResults');
      
      cy.getByTestId('search-input').type('nonexistentquery123');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('emptyResults');
      
      // Verify no results message
      cy.getByTestId('no-results')
        .should('be.visible')
        .should('contain', 'No results found');
      
      // Verify suggestions for alternative searches
      cy.getByTestId('search-suggestions-alternative')
        .should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load search results within acceptable time', () => {
      const startTime = Date.now();
      
      cy.getByTestId('search-input').type('javascript');
      cy.getByTestId('search-button').click();
      
      cy.waitForApiCall('searchResults').then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(3000); // 3 seconds max
      });
      
      cy.getByTestId('search-results').should('be.visible');
    });

    it('should debounce search suggestions', () => {
      // Type quickly
      cy.getByTestId('search-input').type('javascript', { delay: 50 });
      
      // Should only make one API call after debounce
      cy.wait(1000);
      cy.get('@searchSuggestions.all').should('have.length', 1);
    });
  });

  describe('Accessibility', () => {
    it('should be keyboard navigable', () => {
      // Tab to search input
      cy.get('body').tab();
      cy.getByTestId('search-input').should('have.focus');
      
      // Type and navigate suggestions with keyboard
      cy.getByTestId('search-input').type('java');
      cy.waitForApiCall('searchSuggestions');
      
      // Use arrow keys to navigate suggestions
      cy.getByTestId('search-input').type('{downarrow}');
      cy.getByTestId('suggestion-0').should('have.class', 'selected');
      
      // Select with Enter
      cy.getByTestId('search-input').type('{enter}');
      cy.waitForApiCall('searchResults');
    });

    it('should have proper ARIA labels', () => {
      cy.verifyNoAccessibilityViolations();
      
      // Check search input accessibility
      cy.getByTestId('search-input')
        .should('have.attr', 'aria-label')
        .should('have.attr', 'role');
      
      // Check results accessibility
      cy.getByTestId('search-input').type('test');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchResults');
      
      cy.getByTestId('search-results')
        .should('have.attr', 'role', 'region')
        .should('have.attr', 'aria-label');
    });
  });

  describe('Mobile Responsiveness', () => {
    it('should work on mobile devices', () => {
      cy.viewport('iphone-x');
      
      // Verify mobile layout
      cy.getByTestId('search-input').should('be.visible');
      cy.getByTestId('search-button').should('be.visible');
      
      // Perform search on mobile
      cy.getByTestId('search-input').type('mobile test');
      cy.getByTestId('search-button').click();
      cy.waitForApiCall('searchResults');
      
      // Verify results display properly on mobile
      cy.getByTestId('search-results').should('be.visible');
      cy.checkResponsive(['mobile']);
    });
  });
});
