/**
 * E2E tests for authentication functionality
 * 
 * This demonstrates comprehensive authentication testing including:
 * - Login/logout workflows
 * - Form validation
 * - Error handling
 * - Security features
 * - Accessibility
 */

import { LoginPage } from '../../support/page-objects/LoginPage';

describe('Authentication', () => {
  const loginPage = new LoginPage();

  beforeEach(() => {
    // Setup test environment
    cy.setupTestEnvironment();
    
    // Setup authentication intercepts
    cy.setupAuthIntercepts();
  });

  afterEach(() => {
    // Clean up test data
    cy.resetTestEnvironment();
  });

  describe('Login Page', () => {
    beforeEach(() => {
      loginPage.visit();
    });

    it('should display login form correctly', () => {
      loginPage
        .shouldBeOnLoginPage()
        .getEmailInput().should('be.visible')
        .getPasswordInput().should('be.visible')
        .getLoginButton().should('be.visible')
        .getForgotPasswordLink().should('be.visible')
        .getSignUpLink().should('be.visible');
    });

    it('should have proper form labels and accessibility', () => {
      loginPage.checkAccessibility();
      
      // Check form labels
      loginPage.getEmailInput()
        .should('have.attr', 'aria-label')
        .should('have.attr', 'type', 'email');
      
      loginPage.getPasswordInput()
        .should('have.attr', 'aria-label')
        .should('have.attr', 'type', 'password');
      
      loginPage.getLoginButton()
        .should('have.attr', 'type', 'submit');
    });

    it('should support keyboard navigation', () => {
      loginPage.checkKeyboardNavigation();
    });
  });

  describe('Successful Login', () => {
    beforeEach(() => {
      loginPage.visit();
      loginPage.setupSuccessfulLoginMock();
    });

    it('should login with valid credentials', () => {
      loginPage
        .enterEmail('<EMAIL>')
        .enterPassword('password123')
        .clickLogin()
        .waitForLoginRequest()
        .shouldRedirectAfterLogin('/dashboard');
      
      // Verify user is logged in
      cy.getByTestId('user-menu').should('be.visible');
      cy.getByTestId('user-name').should('contain', 'Test User');
    });

    it('should remember login when checkbox is checked', () => {
      loginPage
        .enterEmail('<EMAIL>')
        .enterPassword('password123')
        .toggleRememberMe()
        .clickLogin()
        .waitForLoginRequest();
      
      // Verify remember me was sent in request
      cy.wait('@loginRequest').then((interception) => {
        expect(interception.request.body).to.have.property('rememberMe', true);
      });
    });

    it('should handle redirect after login', () => {
      // Visit protected page first
      cy.visit('/profile');
      
      // Should redirect to login with return URL
      cy.url().should('include', '/user/login');
      cy.url().should('include', 'redirect=');
      
      // Login and verify redirect back to original page
      loginPage
        .loginWithValidCredentials()
        .waitForLoginRequest();
      
      cy.url().should('include', '/profile');
    });
  });

  describe('Failed Login', () => {
    beforeEach(() => {
      loginPage.visit();
      loginPage.setupFailedLoginMock();
    });

    it('should show error for invalid credentials', () => {
      loginPage
        .enterEmail('<EMAIL>')
        .enterPassword('wrongpassword')
        .clickLogin()
        .waitForLoginRequest()
        .shouldShowErrorMessage('Invalid email or password')
        .shouldBeOnLoginPage();
    });

    it('should clear error when user starts typing', () => {
      // First trigger an error
      loginPage
        .loginWithInvalidCredentials()
        .waitForLoginRequest()
        .shouldShowErrorMessage();
      
      // Error should clear when user starts typing
      loginPage
        .enterEmail('<EMAIL>')
        .shouldNotShowErrorMessage();
    });

    it('should handle network errors gracefully', () => {
      // Mock network error
      cy.mockApiError('POST', '/auth/sign_in.json', 500, 'networkError');
      
      loginPage
        .loginWithValidCredentials()
        .waitForApiCall('networkError')
        .shouldShowErrorMessage('Unable to connect. Please try again.');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      loginPage.visit();
    });

    it('should validate required fields', () => {
      loginPage
        .clickLogin()
        .shouldShowEmailValidationError()
        .shouldShowPasswordValidationError()
        .shouldBeOnLoginPage();
    });

    it('should validate email format', () => {
      loginPage
        .enterEmail('invalid-email')
        .enterPassword('password123')
        .clickLogin()
        .shouldShowEmailValidationError();
    });

    it('should disable login button when form is invalid', () => {
      loginPage.shouldHaveDisabledLoginButton();
      
      loginPage.enterEmail('<EMAIL>');
      loginPage.shouldHaveDisabledLoginButton();
      
      loginPage.enterPassword('password123');
      loginPage.shouldHaveEnabledLoginButton();
    });

    it('should show password strength indicator', () => {
      loginPage.enterPassword('weak');
      cy.getByTestId('password-strength')
        .should('be.visible')
        .should('contain', 'Weak');
      
      loginPage.getPasswordInput().clear().type('StrongPassword123!');
      cy.getByTestId('password-strength')
        .should('contain', 'Strong');
    });
  });

  describe('Security Features', () => {
    beforeEach(() => {
      loginPage.visit();
    });

    it('should mask password input', () => {
      loginPage.testPasswordMasking();
    });

    it('should prevent form submission with empty fields', () => {
      loginPage.testFormSubmissionPrevention();
    });

    it('should handle account lockout', () => {
      // Mock account lockout response
      cy.mockApiResponse('POST', '/auth/sign_in.json', {
        success: false,
        error: 'Account temporarily locked due to multiple failed attempts',
        lockoutTime: 300
      }, 'accountLocked');
      
      loginPage
        .loginWithInvalidCredentials()
        .waitForApiCall('accountLocked')
        .shouldShowErrorMessage('Account temporarily locked');
      
      // Login button should be disabled
      loginPage.shouldHaveDisabledLoginButton();
    });

    it('should handle session timeout', () => {
      // Mock session timeout
      cy.mockApiResponse('POST', '/auth/sign_in.json', {
        success: false,
        error: 'Session expired'
      }, 'sessionTimeout');
      
      loginPage
        .loginWithValidCredentials()
        .waitForApiCall('sessionTimeout')
        .shouldShowErrorMessage('Your session has expired. Please login again.');
    });
  });

  describe('SSO Integration', () => {
    beforeEach(() => {
      loginPage.visit();
    });

    it('should display SSO login option', () => {
      loginPage.getSSOButton()
        .should('be.visible')
        .should('contain', 'Sign in with SSO');
    });

    it('should redirect to SSO provider', () => {
      // Mock SSO redirect
      cy.window().then((win) => {
        cy.stub(win, 'open').as('ssoRedirect');
      });
      
      loginPage.clickSSOLogin();
      
      cy.get('@ssoRedirect').should('have.been.calledWith', 
        Cypress.sinon.match(/sso\/auth/));
    });
  });

  describe('Logout', () => {
    beforeEach(() => {
      // Login first
      cy.loginAsUser();
      cy.visit('/dashboard');
    });

    it('should logout successfully', () => {
      cy.logout();
      
      // Should redirect to login page
      cy.url().should('include', '/user/login');
      
      // User menu should not be visible
      cy.getByTestId('user-menu').should('not.exist');
    });

    it('should clear session data on logout', () => {
      cy.logout();
      
      // Verify session storage is cleared
      cy.window().then((win) => {
        expect(win.sessionStorage.getItem('authToken')).to.be.null;
        expect(win.localStorage.getItem('userPreferences')).to.be.null;
      });
    });

    it('should handle logout errors gracefully', () => {
      // Mock logout error
      cy.mockApiError('DELETE', '/auth/sign_out.json', 500, 'logoutError');
      
      cy.logout();
      
      // Should still redirect to login even if API fails
      cy.url().should('include', '/user/login');
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile devices', () => {
      loginPage.testMobileLayout();
      
      loginPage
        .enterEmail('<EMAIL>')
        .enterPassword('password123');
      
      // Verify mobile-specific elements
      cy.getByTestId('mobile-login-header').should('be.visible');
    });

    it('should work on tablet devices', () => {
      loginPage.testTabletLayout();
      
      // Verify tablet layout
      loginPage.getLoginForm().should('have.css', 'max-width');
    });

    it('should work on desktop', () => {
      loginPage.testDesktopLayout();
      
      // Verify desktop layout features
      cy.getByTestId('login-sidebar').should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load login page quickly', () => {
      const startTime = Date.now();
      
      loginPage.visit();
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).to.be.lessThan(3000);
    });

    it('should login within acceptable time', () => {
      loginPage.visit();
      loginPage.setupSuccessfulLoginMock();
      
      loginPage
        .enterEmail('<EMAIL>')
        .enterPassword('password123');
      
      loginPage.measureLoginTime().then((loginTime) => {
        expect(loginTime).to.be.lessThan(2000);
      });
    });
  });
});
