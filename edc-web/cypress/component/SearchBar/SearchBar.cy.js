/**
 * Simple component test for SearchBar component
 */

import React from 'react';

// Simple SearchBar component for testing
const SearchBar = ({ 
  placeholder = 'Search...', 
  onSearch, 
  'data-testid': testId = 'search-bar'
}) => {
  const [query, setQuery] = React.useState('');

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    
    if (onSearch) {
      onSearch(value);
    }
  };

  return React.createElement(
    'div',
    { 
      className: 'search-bar', 
      'data-testid': testId 
    },
    React.createElement('input', {
      type: 'text',
      value: query,
      onChange: handleInputChange,
      placeholder: placeholder,
      className: 'search-input',
      'data-testid': 'search-input'
    })
  );
};

describe('SearchBar Component', () => {
  it('should render with default placeholder', () => {
    cy.mount(React.createElement(SearchBar));
    
    cy.get('[data-testid="search-bar"]').should('exist');
    cy.get('[data-testid="search-input"]')
      .should('exist')
      .should('have.attr', 'placeholder', 'Search...')
      .should('have.value', '');
  });

  it('should update input value when typing', () => {
    cy.mount(React.createElement(SearchBar));
    
    const searchQuery = 'javascript';
    
    cy.get('[data-testid="search-input"]')
      .type(searchQuery)
      .should('have.value', searchQuery);
  });

  it('should call onSearch when typing', () => {
    const onSearchSpy = cy.stub().as('onSearchSpy');
    
    cy.mount(React.createElement(SearchBar, { onSearch: onSearchSpy }));
    
    cy.get('[data-testid="search-input"]').type('test');
    
    // Should be called for each character
    cy.get('@onSearchSpy').should('have.been.calledWith', 't');
    cy.get('@onSearchSpy').should('have.been.calledWith', 'te');
    cy.get('@onSearchSpy').should('have.been.calledWith', 'tes');
    cy.get('@onSearchSpy').should('have.been.calledWith', 'test');
  });
});
