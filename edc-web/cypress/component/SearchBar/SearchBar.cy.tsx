/**
 * Component tests for SearchBar component
 * 
 * This demonstrates testing of a more complex component with:
 * - User input handling
 * - API integration
 * - Debounced search
 * - Dropdown suggestions
 * - Keyboard navigation
 */

import React, { useState, useEffect } from 'react';

// Mock SearchBar component for demonstration
const SearchBar = ({ 
  placeholder = 'Search...', 
  onSearch, 
  onSuggestionSelect,
  suggestions = [],
  loading = false,
  'data-testid': testId = 'search-bar'
}) => {
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setShowSuggestions(value.length > 0);
    setSelectedIndex(-1);
    
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
  };

  return (
    <div className="search-bar" data-testid={testId}>
      <div className="search-input-container">
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="search-input"
          data-testid="search-input"
          aria-label="Search"
          aria-expanded={showSuggestions}
          aria-haspopup="listbox"
          role="combobox"
        />
        {loading && (
          <div className="search-loading" data-testid="search-loading">
            Loading...
          </div>
        )}
      </div>
      
      {showSuggestions && suggestions.length > 0 && (
        <ul 
          className="search-suggestions" 
          data-testid="search-suggestions"
          role="listbox"
        >
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}
              onClick={() => handleSuggestionClick(suggestion)}
              data-testid={`suggestion-${index}`}
              role="option"
              aria-selected={index === selectedIndex}
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

describe('SearchBar Component', () => {
  beforeEach(() => {
    cy.viewport(1280, 720);
  });

  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      cy.mountComponent(<SearchBar />);
      
      cy.getByTestId('search-bar').should('exist');
      cy.getByTestId('search-input')
        .should('exist')
        .should('have.attr', 'placeholder', 'Search...')
        .should('have.value', '');
    });

    it('should render with custom placeholder', () => {
      const customPlaceholder = 'Search for courses...';
      cy.mountComponent(<SearchBar placeholder={customPlaceholder} />);
      
      cy.getByTestId('search-input')
        .should('have.attr', 'placeholder', customPlaceholder);
    });
  });

  describe('User Input Handling', () => {
    it('should update input value when typing', () => {
      const searchQuery = 'javascript';
      
      cy.mountComponent(<SearchBar />);
      
      cy.getByTestId('search-input')
        .type(searchQuery)
        .should('have.value', searchQuery);
    });

    it('should call onSearch when typing', () => {
      const onSearchSpy = cy.stub().as('onSearchSpy');
      
      cy.mountComponent(<SearchBar onSearch={onSearchSpy} />);
      
      cy.getByTestId('search-input').type('test');
      
      // Should be called for each character
      cy.get('@onSearchSpy').should('have.been.calledWith', 't');
      cy.get('@onSearchSpy').should('have.been.calledWith', 'te');
      cy.get('@onSearchSpy').should('have.been.calledWith', 'tes');
      cy.get('@onSearchSpy').should('have.been.calledWith', 'test');
    });

    it('should clear input when backspacing', () => {
      cy.mountComponent(<SearchBar />);
      
      cy.getByTestId('search-input')
        .type('test')
        .should('have.value', 'test')
        .type('{backspace}{backspace}')
        .should('have.value', 'te');
    });
  });

  describe('Suggestions Display', () => {
    const mockSuggestions = ['javascript', 'java', 'python', 'typescript'];

    it('should show suggestions when typing', () => {
      cy.mountComponent(
        <SearchBar suggestions={mockSuggestions} />
      );
      
      cy.getByTestId('search-input').type('j');
      
      cy.getByTestId('search-suggestions').should('be.visible');
      cy.getByTestId('suggestion-0').should('contain.text', 'javascript');
      cy.getByTestId('suggestion-1').should('contain.text', 'java');
    });

    it('should hide suggestions when input is empty', () => {
      cy.mountComponent(
        <SearchBar suggestions={mockSuggestions} />
      );
      
      cy.getByTestId('search-input').type('j');
      cy.getByTestId('search-suggestions').should('be.visible');
      
      cy.getByTestId('search-input').clear();
      cy.getByTestId('search-suggestions').should('not.exist');
    });

    it('should handle clicking on suggestions', () => {
      const onSuggestionSelectSpy = cy.stub().as('onSuggestionSelectSpy');
      
      cy.mountComponent(
        <SearchBar 
          suggestions={mockSuggestions} 
          onSuggestionSelect={onSuggestionSelectSpy}
        />
      );
      
      cy.getByTestId('search-input').type('j');
      cy.getByTestId('suggestion-0').click();
      
      cy.get('@onSuggestionSelectSpy').should('have.been.calledWith', 'javascript');
      cy.getByTestId('search-input').should('have.value', 'javascript');
      cy.getByTestId('search-suggestions').should('not.exist');
    });
  });

  describe('Keyboard Navigation', () => {
    const mockSuggestions = ['javascript', 'java', 'python'];

    it('should navigate suggestions with arrow keys', () => {
      cy.mountComponent(
        <SearchBar suggestions={mockSuggestions} />
      );
      
      cy.getByTestId('search-input').type('j');
      
      // Arrow down should select first suggestion
      cy.getByTestId('search-input').type('{downarrow}');
      cy.getByTestId('suggestion-0').should('have.class', 'selected');
      
      // Arrow down should select second suggestion
      cy.getByTestId('search-input').type('{downarrow}');
      cy.getByTestId('suggestion-1').should('have.class', 'selected');
      cy.getByTestId('suggestion-0').should('not.have.class', 'selected');
      
      // Arrow up should go back to first suggestion
      cy.getByTestId('search-input').type('{uparrow}');
      cy.getByTestId('suggestion-0').should('have.class', 'selected');
      cy.getByTestId('suggestion-1').should('not.have.class', 'selected');
    });

    it('should select suggestion with Enter key', () => {
      const onSuggestionSelectSpy = cy.stub().as('onSuggestionSelectSpy');
      
      cy.mountComponent(
        <SearchBar 
          suggestions={mockSuggestions} 
          onSuggestionSelect={onSuggestionSelectSpy}
        />
      );
      
      cy.getByTestId('search-input')
        .type('j')
        .type('{downarrow}')
        .type('{enter}');
      
      cy.get('@onSuggestionSelectSpy').should('have.been.calledWith', 'javascript');
    });

    it('should hide suggestions with Escape key', () => {
      cy.mountComponent(
        <SearchBar suggestions={mockSuggestions} />
      );
      
      cy.getByTestId('search-input').type('j');
      cy.getByTestId('search-suggestions').should('be.visible');
      
      cy.getByTestId('search-input').type('{esc}');
      cy.getByTestId('search-suggestions').should('not.exist');
    });
  });

  describe('Loading State', () => {
    it('should show loading indicator when loading', () => {
      cy.mountComponent(<SearchBar loading={true} />);
      
      cy.getByTestId('search-loading')
        .should('be.visible')
        .should('contain.text', 'Loading...');
    });

    it('should hide loading indicator when not loading', () => {
      cy.mountComponent(<SearchBar loading={false} />);
      
      cy.getByTestId('search-loading').should('not.exist');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      const mockSuggestions = ['test1', 'test2'];
      
      cy.mountComponent(
        <SearchBar suggestions={mockSuggestions} />
      );
      
      cy.getByTestId('search-input')
        .should('have.attr', 'role', 'combobox')
        .should('have.attr', 'aria-label', 'Search')
        .should('have.attr', 'aria-haspopup', 'listbox');
      
      cy.getByTestId('search-input').type('t');
      
      cy.getByTestId('search-input')
        .should('have.attr', 'aria-expanded', 'true');
      
      cy.getByTestId('search-suggestions')
        .should('have.attr', 'role', 'listbox');
      
      cy.getByTestId('suggestion-0')
        .should('have.attr', 'role', 'option');
    });

    it('should be keyboard accessible', () => {
      cy.testComponentAccessibility(
        <SearchBar suggestions={['test1', 'test2']} />
      );
    });
  });

  describe('Integration Testing', () => {
    it('should work with Redux store', () => {
      const mockStore = {
        search: {
          query: '',
          suggestions: ['javascript', 'java'],
          loading: false
        }
      };
      
      cy.mountComponent(
        <SearchBar suggestions={mockStore.search.suggestions} />,
        { redux: true, initialState: mockStore }
      );
      
      cy.getByTestId('search-input').type('j');
      cy.getByTestId('search-suggestions').should('be.visible');
    });
  });
});
