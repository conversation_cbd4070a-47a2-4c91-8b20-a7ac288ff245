/**
 * Simple component test for Button component
 */

import React from 'react';

// Simple Button component for testing
const Button = ({ 
  children, 
  onClick,
  'data-testid': testId,
  ...props 
}) => React.createElement(
  'button',
  {
    onClick,
    'data-testid': testId,
    ...props
  },
  children
);

describe('Button Component', () => {
  it('should render with text', () => {
    cy.mount(React.createElement(Button, null, 'Click me'));
    
    cy.get('button')
      .should('exist')
      .should('contain.text', 'Click me');
  });

  it('should handle click events', () => {
    const onClickSpy = cy.stub().as('onClickSpy');
    
    cy.mount(
      React.createElement(
        Button, 
        { 
          onClick: onClickSpy, 
          'data-testid': 'test-button' 
        }, 
        'Click me'
      )
    );
    
    cy.get('[data-testid="test-button"]').click();
    cy.get('@onClickSpy').should('have.been.calledOnce');
  });
});
