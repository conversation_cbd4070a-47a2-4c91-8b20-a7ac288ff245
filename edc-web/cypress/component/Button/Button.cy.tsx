/**
 * Component tests for Button component
 * 
 * This file demonstrates comprehensive component testing patterns including:
 * - Basic rendering tests
 * - Props testing
 * - Event handling
 * - Accessibility testing
 * - Visual regression testing
 */

import React from 'react';

// Mock Button component for demonstration
const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  disabled = false, 
  onClick,
  'data-testid': testId,
  ...props 
}) => (
  <button
    className={`btn btn-${variant} btn-${size} ${disabled ? 'disabled' : ''}`}
    disabled={disabled}
    onClick={onClick}
    data-testid={testId}
    {...props}
  >
    {children}
  </button>
);

describe('Button Component', () => {
  beforeEach(() => {
    // Setup common test environment
    cy.viewport(1280, 720);
  });

  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      cy.mountComponent(<Button>Click me</Button>);
      
      cy.get('button')
        .should('exist')
        .should('contain.text', 'Click me')
        .should('have.class', 'btn-primary')
        .should('have.class', 'btn-medium');
    });

    it('should render with custom text', () => {
      const buttonText = 'Custom Button Text';
      cy.mountComponent(<Button>{buttonText}</Button>);
      
      cy.get('button').should('contain.text', buttonText);
    });

    it('should render with data-testid', () => {
      cy.mountComponent(<Button data-testid="test-button">Test</Button>);
      
      cy.getByTestId('test-button').should('exist');
    });
  });

  describe('Props Testing', () => {
    const propVariations = [
      { variant: 'primary', expectedClass: 'btn-primary' },
      { variant: 'secondary', expectedClass: 'btn-secondary' },
      { variant: 'danger', expectedClass: 'btn-danger' },
    ];

    propVariations.forEach(({ variant, expectedClass }) => {
      it(`should render ${variant} variant correctly`, () => {
        cy.mountComponent(<Button variant={variant}>Button</Button>);
        
        cy.get('button').should('have.class', expectedClass);
      });
    });

    const sizeVariations = [
      { size: 'small', expectedClass: 'btn-small' },
      { size: 'medium', expectedClass: 'btn-medium' },
      { size: 'large', expectedClass: 'btn-large' },
    ];

    sizeVariations.forEach(({ size, expectedClass }) => {
      it(`should render ${size} size correctly`, () => {
        cy.mountComponent(<Button size={size}>Button</Button>);
        
        cy.get('button').should('have.class', expectedClass);
      });
    });

    it('should handle disabled state', () => {
      cy.mountComponent(<Button disabled>Disabled Button</Button>);
      
      cy.get('button')
        .should('be.disabled')
        .should('have.class', 'disabled');
    });
  });

  describe('Event Handling', () => {
    it('should call onClick when clicked', () => {
      const onClickSpy = cy.stub().as('onClickSpy');
      
      cy.mountComponent(
        <Button onClick={onClickSpy} data-testid="clickable-button">
          Click me
        </Button>
      );
      
      cy.getByTestId('clickable-button').click();
      cy.get('@onClickSpy').should('have.been.calledOnce');
    });

    it('should not call onClick when disabled', () => {
      const onClickSpy = cy.stub().as('onClickSpy');
      
      cy.mountComponent(
        <Button onClick={onClickSpy} disabled data-testid="disabled-button">
          Disabled
        </Button>
      );
      
      cy.getByTestId('disabled-button').click({ force: true });
      cy.get('@onClickSpy').should('not.have.been.called');
    });

    it('should handle keyboard events', () => {
      const onClickSpy = cy.stub().as('onClickSpy');
      
      cy.mountComponent(
        <Button onClick={onClickSpy} data-testid="keyboard-button">
          Keyboard Test
        </Button>
      );
      
      cy.getByTestId('keyboard-button')
        .focus()
        .type('{enter}');
      
      cy.get('@onClickSpy').should('have.been.calledOnce');
    });
  });

  describe('Accessibility Testing', () => {
    it('should be accessible', () => {
      cy.mountComponent(<Button>Accessible Button</Button>);
      
      cy.testComponentAccessibility(<Button>Accessible Button</Button>);
    });

    it('should have proper focus management', () => {
      cy.mountComponent(<Button data-testid="focus-button">Focus Test</Button>);
      
      cy.getByTestId('focus-button')
        .focus()
        .should('have.focus')
        .should('have.css', 'outline');
    });

    it('should have proper ARIA attributes when disabled', () => {
      cy.mountComponent(
        <Button disabled aria-label="Disabled button">
          Disabled
        </Button>
      );
      
      cy.get('button')
        .should('have.attr', 'aria-label', 'Disabled button')
        .should('have.attr', 'disabled');
    });
  });

  describe('Visual Testing', () => {
    it('should match visual snapshots for different variants', () => {
      const variants = ['primary', 'secondary', 'danger'];
      
      variants.forEach(variant => {
        cy.mountComponent(<Button variant={variant}>{variant} Button</Button>);
        cy.screenshot(`button-${variant}`);
      });
    });

    it('should be responsive', () => {
      cy.testComponentResponsive(<Button>Responsive Button</Button>);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty children', () => {
      cy.mountComponent(<Button></Button>);
      
      cy.get('button').should('exist').should('be.empty');
    });

    it('should handle complex children', () => {
      cy.mountComponent(
        <Button>
          <span>Icon</span>
          <span>Text</span>
        </Button>
      );
      
      cy.get('button')
        .should('contain', 'Icon')
        .should('contain', 'Text');
    });

    it('should handle long text', () => {
      const longText = 'This is a very long button text that might cause layout issues';
      
      cy.mountComponent(<Button>{longText}</Button>);
      
      cy.get('button')
        .should('contain.text', longText)
        .should('be.visible');
    });
  });

  describe('Integration with Theme', () => {
    it('should apply custom theme styles', () => {
      const customTheme = {
        colors: {
          primary: '#custom-color'
        }
      };
      
      cy.mountComponent(<Button variant="primary">Themed Button</Button>, {
        theme: customTheme
      });
      
      // Verify theme application (would depend on actual theme implementation)
      cy.get('button').should('exist');
    });
  });
});
