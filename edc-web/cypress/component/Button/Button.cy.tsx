/**
 * Simple component test for Button component
 */

import React from 'react';

// Simple Button component for testing
const Button = ({
  children,
  onClick,
  'data-testid': testId,
  ...props
}) => (
  <button
    onClick={onClick}
    data-testid={testId}
    {...props}
  >
    {children}
  </button>
);

describe('Button Component', () => {
  it('should render with text', () => {
    cy.mount(<Button>Click me</Button>);

    cy.get('button')
      .should('exist')
      .should('contain.text', 'Click me');
  });

  it('should handle click events', () => {
    const onClickSpy = cy.stub().as('onClickSpy');

    cy.mount(
      <Button onClick={onClickSpy} data-testid="test-button">
        Click me
      </Button>
    );

    cy.get('[data-testid="test-button"]').click();
    cy.get('@onClickSpy').should('have.been.calledOnce');
  });

});
