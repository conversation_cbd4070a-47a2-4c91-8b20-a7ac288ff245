import { defineConfig } from "cypress";

export default defineConfig({
  // Global configuration
  viewportWidth: 1280,
  viewportHeight: 720,
  video: true,
  screenshotOnRunFailure: true,
  defaultCommandTimeout: 10000,
  requestTimeout: 10000,
  responseTimeout: 10000,
  pageLoadTimeout: 30000,

  // Test file patterns
  fixturesFolder: 'cypress/fixtures',
  screenshotsFolder: 'cypress/screenshots',
  videosFolder: 'cypress/videos',
  downloadsFolder: 'cypress/downloads',

  // Component Testing Configuration
  component: {
    devServer: {
      framework: "react",
      bundler: "webpack",
      webpackConfig: {
        devServer: {
          port: 8002, // Use different port to avoid conflict with main dev server
          host: 'localhost',
        },
        resolve: {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
        },
        module: {
          rules: [
            {
              test: /\.(js|jsx|ts|tsx)$/,
              exclude: /node_modules/,
              use: {
                loader: 'babel-loader',
                options: {
                  presets: [
                    ['@babel/preset-env', { targets: 'defaults' }],
                    ['@babel/preset-react', { runtime: 'automatic' }],
                    '@babel/preset-typescript'
                  ]
                }
              }
            },
            {
              test: /\.(css|scss)$/,
              use: ['style-loader', 'css-loader', 'sass-loader']
            }
          ]
        }
      }
    },
    specPattern: 'cypress/component/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: false,
    viewportWidth: 1000,
    viewportHeight: 660,
  },

  // E2E Testing Configuration
  e2e: {
    baseUrl: 'http://local.lvh.me:4001',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.ts',

    // Test isolation and cleanup
    testIsolation: true,

    // Browser configuration
    chromeWebSecurity: false,

    // Retry configuration
    retries: {
      runMode: 2,
      openMode: 0
    },

    setupNodeEvents(on, config) {
      // Terminal report plugin
      require('cypress-terminal-report/src/installLogsPrinter')(on, {
        printLogsToConsole: "always",
        includeSuccessfulHookLogs: true,
        commandTrimLength: 800,
        compactLogs: 1,
      });

      // Task registration for custom tasks
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },

        // Custom task for database seeding (if needed)
        seedDatabase(data) {
          // Implementation for seeding test data
          console.log('Seeding database with:', data);
          return null;
        },

        // Custom task for clearing test data
        clearTestData() {
          console.log('Clearing test data');
          return null;
        }
      });

      // Environment-specific configuration
      if (config.env.environment === 'staging') {
        config.baseUrl = 'https://staging.example.com';
      } else if (config.env.environment === 'production') {
        config.baseUrl = 'https://production.example.com';
      }

      return config;
    },
  },

  // Environment variables
  env: {
    environment: 'development',
    apiUrl: 'http://localhost:8001/api',
    coverage: false,
    hideXHRInCommandLog: false,
  },
});
